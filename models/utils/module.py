from torch import nn

class FilmBlock(nn.Module):
    def __init__(self, config):
        super(FilmBlock, self).__init__()
        self.scale = nn.Linear(config.llm_dim, config.base_config.vision_config.hidden_size)
        self.shift = nn.Linear(config.llm_dim, config.base_config.vision_config.hidden_size)
        nn.init.constant_(self.scale.weight, 0)
        nn.init.constant_(self.scale.bias, 0)
        nn.init.constant_(self.shift.weight, 0)
        nn.init.constant_(self.shift.bias, 0)

    def forward(self,x, language_embeddings):
        """
        x: vision embeddings
        """
        gamma = self.scale(language_embeddings)  # (batch_size, vision_dim)
        beta = self.shift(language_embeddings)  # (batch_size, vision_dim)

        # Modulate intermediate visual representations via FiLM
        x = x * (1 + gamma.view(gamma.shape[0], 1, gamma.shape[1])) + beta.view(beta.shape[0], 1, beta.shape[1])

        return x