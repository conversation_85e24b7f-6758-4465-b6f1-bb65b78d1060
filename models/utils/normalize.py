import torch
import numpy as np
from torch import nn, Tensor
from models.utils.constants import *
class NormalizeMeanStd(nn.Module):
    """Normalizes data (e.g. "observation.image") for more stable and faster convergence during training."""

    def __init__(
        self,
        norm_stats,
        k,
    ):
        """
        This class is used to normalize the data with mean and std.
        """
        super().__init__()
        if k == ACTION:
            key = k
        elif k == OBS_ROBOT:
            key = "qpos"
        if norm_stats is None:
            mean, std = np.zeros((14)), np.zeros((14))
        else:
            mean, std = norm_stats[f'{key}_mean'], norm_stats[f'{key}_std']
        if isinstance(mean, np.ndarray):
            mean = torch.from_numpy(mean)
        if isinstance(std, np.ndarray):
            std = torch.from_numpy(std)

        self.k = k
        self.register_buffer("mean", mean)
        self.register_buffer("std", std)


    @torch.no_grad()
    def forward(self, batch):
        batch[self.k] = (batch[self.k] - self.mean) / self.std
        return batch

    def unnormalize(self, x: Tensor) -> Tensor:
        return (x * self.std) + self.mean

class NormalizeMinMax(nn.Module):
    """Normalizes data (e.g. "observation.image") for more stable and faster convergence during training."""

    def __init__(
        self,
        norm_stats,
        use_quantiles: bool = False,
        k: str = None,
    ):
        """
        This class is used to normalize the data with mean and std.
        """
        super().__init__()
        self.k = k
        self.use_quantiles = use_quantiles
        if k == OBS_ROBOT:
            key = 'qpos'
        elif k == ACTION:
            key = k
        if norm_stats is None:
            q01, q99, min, max = np.zeros((14)), np.zeros((14)), np.zeros((14)), np.zeros((14))
        else:
            q01, q99, min, max = norm_stats[f'{key}_q01'], norm_stats[f'{key}_q99'], norm_stats[f'{key}_min'], norm_stats[f'{key}_max']
        if self.use_quantiles:
            min_val = q01
            max_val = q99
        else:
            min_val = min
            max_val = max
        self.register_buffer("min", torch.from_numpy(min_val))
        self.register_buffer("max", torch.from_numpy(max_val))


    @torch.no_grad()
    def forward(self, batch):
        # follow pi05 https://github.com/Physical-Intelligence/openpi/blob/61dd0e6c5ab2660076fc4e0c71e1c93cdd66339a/src/openpi/transforms.py#L141
        batch[self.k] = (batch[self.k] - self.min) / (self.max - self.min + 1e-6) * 2.0 - 1.0
        return batch

    def unnormalize(self, x: Tensor) -> Tensor:
        return ((x + 1.0) / 2.0 * (self.max - self.min + 1e-6)) + self.min