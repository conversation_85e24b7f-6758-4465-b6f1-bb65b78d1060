from transformers import PretrainedConfig, AutoConfig

class PI0S1S2DenseConfig(PretrainedConfig):
    model_type = "pi0_s1s2_dense"
    def __init__(
            self,
            n_obs_steps: int = 1,
            chunk_size: int = 50,
            n_action_steps: int = 50,
            is_composition: bool = False,
            max_state_dim: int = 32,  # Shorter state and action vectors will be padded
            max_action_dim: int = 32,
            resize_imgs_with_padding: tuple[int, int] = (224, 224),  # Image preprocessing
            empty_cameras: int = 0,  # Add empty images. Used by pi0_aloha_sim which adds the empty
            adapt_to_pi_aloha: bool = False,  # Converts the joint and gripper values from the standard Aloha space to
            use_delta_joint_actions_aloha: bool = False,  # Converts joint dimensions to deltas with respect to the current state before passing to the model.
            tokenizer_max_length: int = 48,  # Tokenizer
            proj_width: int = 1024, # Projector
            num_steps: int = 10,  # Decoding
            use_cache: bool = True,  # Attention utils
            attention_implementation: str = "eager",  # or fa2, flex
            # Finetuning settings
            freeze_vision_encoder: bool = True,
            train_expert_only: bool = False,
            train_state_proj: bool = True,
            stop_gradient: bool = False,
            generate_reasoning: bool = False,
            **kwargs
    ):
        self.n_obs_steps = n_obs_steps
        self.chunk_size = chunk_size
        self.n_action_steps = n_action_steps
        self.is_composition = is_composition
        self.max_state_dim = max_state_dim
        self.max_action_dim = max_action_dim
        self.resize_imgs_with_padding = resize_imgs_with_padding
        self.empty_cameras = empty_cameras
        self.adapt_to_pi_aloha = adapt_to_pi_aloha
        self.use_delta_joint_actions_aloha = use_delta_joint_actions_aloha
        self.tokenizer_max_length = tokenizer_max_length

        self.proj_width = proj_width
        self.num_steps = num_steps
        self.use_cache = use_cache
        self.attention_implementation = attention_implementation
        self.freeze_vision_encoder = freeze_vision_encoder
        self.train_state_proj = train_state_proj

        # custom
        self.stop_gradient = stop_gradient
        self.generate_reasoning = generate_reasoning

        self.train_expert_only = train_expert_only

        super().__init__(**kwargs)

AutoConfig.register("pi0_s1s2_dense", PI0S1S2DenseConfig)

# class PI05Config(PreTrainedConfig):
#     # Input / output structure.
#     n_obs_steps: int = 1
#     chunk_size: int = 50
#     n_action_steps: int = 50
#     is_composition: bool = False
#
#     # custom
#     diffusion_strategy: str = "flow"
#     stop_gradient: bool = False
#     generate_reasoning: bool = False
#
#     normalization_mapping: dict[str, NormalizationMode] = field(
#         default_factory=lambda: {
#             "VISUAL": NormalizationMode.IDENTITY,
#             "STATE": NormalizationMode.MEAN_STD,
#             "ACTION": NormalizationMode.MEAN_STD,
#         }
#     )
#
#     # Shorter state and action vectors will be padded
#     max_state_dim: int = 32
#     max_action_dim: int = 32
#
#     # Image preprocessing
#     resize_imgs_with_padding: tuple[int, int] = (224, 224)
#
#     # Add empty images. Used by pi0_aloha_sim which adds the empty
#     # left and right wrist cameras in addition to the top camera.
#     empty_cameras: int = 0
#
#     # Converts the joint and gripper values from the standard Aloha space to
#     # the space used by the pi internal runtime which was used to train the base model.
#     adapt_to_pi_aloha: bool = False
#
#     # Converts joint dimensions to deltas with respect to the current state before passing to the model.
#     # Gripper dimensions will remain in absolute values.
#     use_delta_joint_actions_aloha: bool = False
#
#     # Tokenizer
#     tokenizer_max_length: int = 48
#
#     # Projector
#     proj_width: int = 1024
#
#     # Decoding
#     num_steps: int = 10
#
#     # Attention utils
#     use_cache: bool = True
#     attention_implementation: str = "eager"  # or fa2, flex
#
#     # Finetuning settings
#     freeze_vision_encoder: bool = True
#     train_expert_only: bool = False
#     train_state_proj: bool = True
#
#     # Training presets
#     optimizer_lr: float = 2.5e-5
#     optimizer_betas: tuple[float, float] = (0.9, 0.95)
#     optimizer_eps: float = 1e-8
#     optimizer_weight_decay: float = 1e-10
#
#     scheduler_warmup_steps: int = 1_000
#     scheduler_decay_steps: int = 30_000
#     scheduler_decay_lr: float = 2.5e-6
#
#     # TODO: Add EMA
#
#     def __post_init__(self):
#         super().__post_init__()
#
#         """Input validation (not exhaustive)."""
#         if self.n_action_steps > self.chunk_size:
#             raise ValueError(
#                 f"The chunk size is the upper bound for the number of action steps per model invocation. Got "
#                 f"{self.n_action_steps} for `n_action_steps` and {self.chunk_size} for `chunk_size`."
#             )
#         if self.n_obs_steps != 1:
#             raise ValueError(
#                 f"Multiple observation steps not handled yet. Got `nobs_steps={self.n_obs_steps}`"
#             )
#
#         if self.use_delta_joint_actions_aloha:
#             raise NotImplementedError(
#                 "`use_delta_joint_actions_aloha` is used by mirro for aloha real models. It is not ported yet in LeRobot."
#             )
#
#     def validate_features(self) -> None:
#         # TODO: implement value error
#         # if not self.image_features and not self.env_state_feature:
#         #     raise ValueError("You must provide at least one image or the environment state among the inputs.")
#
#         for i in range(self.empty_cameras):
#             key = f"observation.images.empty_camera_{i}"
#             empty_camera = PolicyFeature(
#                 type=FeatureType.VISUAL,
#                 shape=(3, 480, 640),
#             )
#             self.input_features[key] = empty_camera
#
#     def get_optimizer_preset(self) -> AdamWConfig:
#         return AdamWConfig(
#             lr=self.optimizer_lr,
#             betas=self.optimizer_betas,
#             eps=self.optimizer_eps,
#             weight_decay=self.optimizer_weight_decay,
#         )
#
#     def get_scheduler_preset(self):
#         return CosineDecayWithWarmupSchedulerConfig(
#             peak_lr=self.optimizer_lr,
#             decay_lr=self.scheduler_decay_lr,
#             num_warmup_steps=self.scheduler_warmup_steps,
#             num_decay_steps=self.scheduler_decay_steps,
#         )
#
#     @property
#     def observation_delta_indices(self) -> None:
#         return None
#
#     @property
#     def action_delta_indices(self) -> list:
#         return list(range(self.chunk_size))
#
#     @property
#     def reward_delta_indices(self) -> None:
#         return None
#
#     def to_dict(self) -> Dict[str, Any]:
#         """
#         Serializes this instance to a Python dictionary.
#
#         Returns:
#             `Dict[str, Any]`: Dictionary of all the attributes that make up this configuration instance.
#         """
#         output = copy.deepcopy(self.__dict__)
#         if hasattr(self.__class__, "model_type"):
#             output["model_type"] = self.__class__.model_type
#         if "_auto_class" in output:
#             del output["_auto_class"]
#         if "_commit_hash" in output:
#             del output["_commit_hash"]
#         if "_attn_implementation_internal" in output:
#             del output["_attn_implementation_internal"]
#         # Do not serialize `base_model_tp_plan` for now
#         if "base_model_tp_plan" in output:
#             del output["base_model_tp_plan"]
#
#         # Transformers version when serializing the model
#         output["transformers_version"] = "4.45.2"
#
#         for key, value in output.items():
#             # Deal with nested configs like CLIP
#             if isinstance(value, PretrainedConfig):
#                 value = value.to_dict()
#                 del value["transformers_version"]
#
#             output[key] = value
#
#         if hasattr(self, "quantization_config"):
#             output["quantization_config"] = (
#                 self.quantization_config.to_dict()
#                 if not isinstance(self.quantization_config, dict)
#                 else self.quantization_config
#             )
#
#             # pop the `_pre_quantization_dtype` as torch.dtypes are not serializable.
#             _ = output.pop("_pre_quantization_dtype", None)
#
#         self.dict_torch_dtype_to_str(output)
#
#         return output
#
#     def dict_torch_dtype_to_str(self, d: Dict[str, Any]) -> None:
#         """
#         Checks whether the passed dictionary and its nested dicts have a *torch_dtype* key and if it's not None,
#         converts torch.dtype to a string of just the type. For example, `torch.float32` get converted into *"float32"*
#         string, which can then be stored in the json format.
#         """
#         if d.get("torch_dtype", None) is not None and not isinstance(d["torch_dtype"], str):
#             d["torch_dtype"] = str(d["torch_dtype"]).split(".")[1]
#         for value in d.values():
#             if isinstance(value, dict):
#                 self.dict_torch_dtype_to_str(value)
#
#     def to_json_string(self, use_diff: bool = True) -> str:
#         """
#         Serializes this instance to a JSON string.
#
#         Args:
#             use_diff (`bool`, *optional*, defaults to `True`):
#                 If set to `True`, only the difference between the config instance and the default `PretrainedConfig()`
#                 is serialized to JSON string.
#
#         Returns:
#             `str`: String containing all the attributes that make up this configuration instance in JSON format.
#         """
#         if use_diff is True:
#             config_dict = self.to_diff_dict()
#         else:
#             config_dict = self.to_dict()
#
#         save_dict = copy.deepcopy(config_dict)
#         for key in ['normalization_mapping', 'input_features', 'output_features']:
#             del save_dict[key]
#
#         return json.dumps(save_dict, indent=2, sort_keys=True) + "\n"
#
#     def to_json_file(self, json_file_path: Union[str, os.PathLike], use_diff: bool = True):
#         """
#         Save this instance to a JSON file.
#
#         Args:
#             json_file_path (`str` or `os.PathLike`):
#                 Path to the JSON file in which this configuration instance's parameters will be saved.
#             use_diff (`bool`, *optional*, defaults to `True`):
#                 If set to `True`, only the difference between the config instance and the default `PretrainedConfig()`
#                 is serialized to JSON file.
#         """
#         with open(json_file_path, "w", encoding="utf-8") as writer:
#             writer.write(self.to_json_string(use_diff=use_diff))
#
#     def to_diff_dict(self) -> Dict[str, Any]:
#         """
#         Removes all attributes from config which correspond to the default config attributes for better readability and
#         serializes to a Python dictionary.
#
#         Returns:
#             `Dict[str, Any]`: Dictionary of all the attributes that make up this configuration instance,
#         """
#         config_dict = self.to_dict()
#
#         # get the default config dict
#         default_config_dict = PretrainedConfig().to_dict()
#
#         # get class specific config dict
#         class_config_dict = self.__class__().to_dict() if not self.is_composition else {}
#
#         serializable_config_dict = {}
#
#         # only serialize values that differ from the default config
#         for key, value in config_dict.items():
#             if (
#                 isinstance(getattr(self, key, None), PretrainedConfig)
#                 and key in class_config_dict
#                 and isinstance(class_config_dict[key], dict)
#             ):
#                 # For nested configs we need to clean the diff recursively
#                 diff = recursive_diff_dict(value, class_config_dict[key], config_obj=getattr(self, key, None))
#                 if "model_type" in value:
#                     # Needs to be set even if it's not in the diff
#                     diff["model_type"] = value["model_type"]
#                 if len(diff) > 0:
#                     serializable_config_dict[key] = diff
#             elif (
#                 key not in default_config_dict
#                 or key == "transformers_version"
#                 or value != default_config_dict[key]
#                 or (key in class_config_dict and value != class_config_dict[key])
#             ):
#                 serializable_config_dict[key] = value
#
#         if hasattr(self, "quantization_config"):
#             serializable_config_dict["quantization_config"] = (
#                 self.quantization_config.to_dict()
#                 if not isinstance(self.quantization_config, dict)
#                 else self.quantization_config
#             )
#
#             # pop the `_pre_quantization_dtype` as torch.dtypes are not serializable.
#             _ = serializable_config_dict.pop("_pre_quantization_dtype", None)
#
#         self.dict_torch_dtype_to_str(serializable_config_dict)
#
#         if "_attn_implementation_internal" in serializable_config_dict:
#             del serializable_config_dict["_attn_implementation_internal"]
#         # Do not serialize `base_model_tp_plan` for now
#         if "base_model_tp_plan" in serializable_config_dict:
#             del serializable_config_dict["base_model_tp_plan"]
#
#         return serializable_config_dict
