import math
from collections import deque
from typing import Any, Optional, <PERSON><PERSON>

import numpy as np
import torch
import torch.nn.functional as F  # noqa: N812
from torch import Tensor, nn

from transformers import PreTrainedModel, AutoTokenizer, AutoModel, AutoProcessor
from torch.nn.utils.rnn import pad_sequence
from models.utils.constants import *
from models.mirro_s2.configuration_mirro_s2 import MIRROS2Config
from models.mirro_s2.paligemma_with_expert_adaptive_LN import (
    PaliGemmaWithExpertConfig,
    PaliGemmaWithExpertModel,
)
from models.utils.utils import get_safe_dtype

from models.utils.normalize import NormalizeMeanStd, NormalizeMinMax

def create_sinusoidal_pos_embedding(
    time: torch.tensor, dimension: int, min_period: float, max_period: float, device="cpu"
) -> Tensor:
    """Computes sine-cosine positional embedding vectors for scalar positions."""
    if dimension % 2 != 0:
        raise ValueError(f"dimension ({dimension}) must be divisible by 2")

    if time.ndim != 1:
        raise ValueError("The time tensor is expected to be of shape `(batch_size, )`.")

    dtype = get_safe_dtype(torch.float64, device.type)
    fraction = torch.linspace(0.0, 1.0, dimension // 2, dtype=dtype, device=device)
    period = min_period * (max_period / min_period) ** fraction

    # Compute the outer product
    scaling_factor = 1.0 / period * 2 * math.pi
    sin_input = scaling_factor[None, :] * time[:, None]
    pos_emb = torch.cat([torch.sin(sin_input), torch.cos(sin_input)], dim=1)
    return pos_emb


def sample_beta(alpha, beta, bsize, device): # 1.5 1.0
    gamma1 = torch.empty((bsize,), device=device).uniform_(0, 1).pow(1 / alpha)
    gamma2 = torch.empty((bsize,), device=device).uniform_(0, 1).pow(1 / beta)
    return gamma1 / (gamma1 + gamma2)


def make_att_2d_masks(pad_masks, att_masks):
    """Copied from big_vision.

    Tokens can attend to valid inputs tokens which have a cumulative mask_ar
    smaller or equal to theirs. This way `mask_ar` int[B, N] can be used to
    setup several types of attention, for example:

      [[1 1 1 1 1 1]]: pure causal attention.

      [[0 0 0 1 1 1]]: prefix-lm attention. The first 3 tokens can attend between
          themselves and the last 3 tokens have a causal attention. The first
          entry could also be a 1 without changing behaviour.

      [[1 0 1 0 1 0 0 1 0 0]]: causal attention between 4 blocks. Tokens of a
          block can attend all previous blocks and all tokens on the same block.

    Args:
      input_mask: bool[B, N] true if its part of the input, false if padding.
      mask_ar: int32[B, N] mask that's 1 where previous tokens cannot depend on
        it and 0 where it shares the same attention mask as the previous token.
    """
    if att_masks.ndim != 2:
        raise ValueError(att_masks.ndim)
    if pad_masks.ndim != 2:
        raise ValueError(pad_masks.ndim)

    cumsum = torch.cumsum(att_masks, dim=1)
    att_2d_masks = cumsum[:, None, :] <= cumsum[:, :, None]
    pad_2d_masks = pad_masks[:, None, :] * pad_masks[:, :, None]
    att_2d_masks = att_2d_masks & pad_2d_masks
    return att_2d_masks


def resize_with_pad(img, width, height, pad_value=-1):
    # assume no-op when width height fits already
    if img.ndim != 4:
        raise ValueError(f"(b,c,h,w) expected, but {img.shape}")

    cur_height, cur_width = img.shape[2:]

    ratio = max(cur_width / width, cur_height / height)
    resized_height = int(cur_height / ratio)
    resized_width = int(cur_width / ratio)
    resized_img = F.interpolate(
        img, size=(resized_height, resized_width), mode="bilinear", align_corners=False
    )

    pad_height = max(0, int(height - resized_height))
    pad_width = max(0, int(width - resized_width))

    # pad on left and top of image
    padded_img = F.pad(resized_img, (pad_width, 0, pad_height, 0), value=pad_value)
    return padded_img


def pad_vector(vector, new_dim):
    """Can be (batch_size x sequence_length x features_dimension)
    or (batch_size x features_dimension)
    """
    if vector.shape[-1] == new_dim:
        return vector
    shape = list(vector.shape)
    current_dim = shape[-1]
    shape[-1] = new_dim
    new_vector = torch.zeros(*shape, dtype=vector.dtype, device=vector.device)
    new_vector[..., :current_dim] = vector
    return new_vector


def normalize(x, min_val, max_val):
    return (x - min_val) / (max_val - min_val)


def unnormalize(x, min_val, max_val):
    return x * (max_val - min_val) + min_val


def safe_arcsin(value):
    # This ensures that the input stays within
    # [−1,1] to avoid invalid values for arcsin
    return torch.arcsin(torch.clamp(value, -1.0, 1.0))


def aloha_gripper_to_angular(value):
    # Aloha transforms the gripper positions into a linear space. The following code
    # reverses this transformation to be consistent with mirro which is pretrained in
    # angular space.
    #
    # These values are coming from the Aloha code:
    # PUPPET_GRIPPER_POSITION_OPEN, PUPPET_GRIPPER_POSITION_CLOSED
    value = unnormalize(value, min_val=0.01844, max_val=0.05800)

    # This is the inverse of the angular to linear transformation inside the Interbotix code.
    def linear_to_radian(linear_position, arm_length, horn_radius):
        value = (horn_radius**2 + linear_position**2 - arm_length**2) / (2 * horn_radius * linear_position)
        return safe_arcsin(value)

    # The constants are taken from the Interbotix code.
    value = linear_to_radian(value, arm_length=0.036, horn_radius=0.022)

    # Normalize to [0, 1].
    # The values 0.4 and 1.5 were measured on an actual Trossen robot.
    return normalize(value, min_val=0.4, max_val=1.5)


def aloha_gripper_from_angular(value):
    # Convert from the gripper position used by mirro to the gripper position that is used by Aloha.
    # Note that the units are still angular but the range is different.

    # The values 0.4 and 1.5 were measured on an actual Trossen robot.
    value = unnormalize(value, min_val=0.4, max_val=1.5)

    # These values are coming from the Aloha code:
    # PUPPET_GRIPPER_JOINT_OPEN, PUPPET_GRIPPER_JOINT_CLOSE
    return normalize(value, min_val=-0.6213, max_val=1.4910)


def aloha_gripper_from_angular_inv(value):
    # Directly inverts the gripper_from_angular function.
    value = unnormalize(value, min_val=-0.6213, max_val=1.4910)
    return normalize(value, min_val=0.4, max_val=1.5)


class MIRROS2Policy(PreTrainedModel):
    """Wrapper class around PI0FlowMatching model to train and run inference within LeRobot."""

    config_class = MIRROS2Config
    name = "mirro_s2"

    def __init__(
        self,
        config: MIRROS2Config,
    ):
        """
        Args:
            config: Policy configuration class instance or None, in which case the default instantiation of
                    the configuration class is used.
        """
        super().__init__(config)
        self.config = config
        self.model = MIRROS2FlowMatching(config)
        if config.action_tokenizer is not None:
            self.fast_action_tokenizer = AutoProcessor.from_pretrained(config.action_tokenizer, trust_remote_code=True, use_fast=False)
        else:
            self.fast_action_tokenizer = None
        self.set_normalize(None)
        self.language_tokenizer = None
        self.reset()

    def set_normalize(self, dataset_stats=None):
        if self.config.norm_mode == "mean_std":
            self.normalize_inputs = NormalizeMeanStd(dataset_stats, k=OBS_ROBOT)
            self.normalize_targets = NormalizeMeanStd(dataset_stats, k=ACTION)
        elif self.config.norm_mode == "min_max":
            self.normalize_inputs = NormalizeMinMax(dataset_stats, use_quantiles=True, k=OBS_ROBOT)
            self.normalize_targets = NormalizeMinMax(dataset_stats, use_quantiles=True, k=ACTION)

    def reset(self):
        """This should be called whenever the environment is reset."""
        self._action_queue = deque([], maxlen=self.config.n_action_steps)

    def get_optim_params(self) -> dict:
        return self.parameters()

    @torch.no_grad
    def select_action(self, batch: dict[str, Tensor], noise: Tensor | None = None) -> tuple[Tensor, Any]:
        """Select a single action given environment observations.

        This method wraps `select_actions` in order to return one action at a time for execution in the
        environment. It works by managing the actions in a queue and only calling `select_actions` when the
        queue is empty.
        """
        self.eval()

        if self.config.adapt_to_pi_aloha:
            batch[OBS_ROBOT] = self._pi_aloha_decode_state(batch[OBS_ROBOT])
        outputs_text = None
        # Action queue logic for n_action_steps > 1. When the action_queue is depleted, populate it by
        # querying the policy.
        if len(self._action_queue) == 0:
            batch = self.normalize_inputs(batch)
            try:
                generate_reasoning = batch['generate_reasoning']
            except:
                generate_reasoning = getattr(self.config, "generate_reasoning", False)
            images, img_masks = self.prepare_images(batch)
            state = self.prepare_state(batch)
            lang_tokens, lang_masks, _ = self.prepare_language(batch, eval=True)

            actions, outputs_text = self.model.sample_actions(
                images, img_masks, lang_tokens, lang_masks, state, noise=noise,
                generate_reasoning=generate_reasoning,
                language_tokenizer=self.language_tokenizer,
            )

            # Unpad actions
            original_action_dim = self.config.action_dim
            actions = actions[:, :, :original_action_dim]

            actions = self.normalize_targets.unnormalize(actions)

            if self.config.adapt_to_pi_aloha:
                actions = self._pi_aloha_encode_actions(actions)

            # `self.model.forward` returns a (batch_size, n_action_steps, action_dim) tensor, but the queue
            # effectively has shape (n_action_steps, batch_size, *), hence the transpose.
            self._action_queue.extend(actions.transpose(0, 1))
        return self._action_queue.popleft(), outputs_text

    def forward(self, batch: dict[str, Tensor], noise=None, time=None) -> dict[str, Tensor]:
        """Do a full training forward pass to compute the loss"""
        batch = self.normalize_inputs(batch)
        batch = self.normalize_targets(batch)

        images, img_masks = self.prepare_images(batch)
        state = self.prepare_state(batch)

        if self.fast_action_tokenizer is not None:
            lang_tokens, lang_masks, labels = self.prepare_language_with_action_tokens(batch)
        else:
            lang_tokens, lang_masks, labels = self.prepare_language(batch)
        actions = self.prepare_action(batch)
        actions_is_pad = batch.get("action_is_pad")

        loss_dict = {}
        losses, loss_lm = self.model.forward(images, img_masks, lang_tokens, lang_masks, state, actions, noise, time, labels)
        loss_dict["losses_after_forward"] = losses.clone()

        if actions_is_pad is not None:
            in_episode_bound = 1-actions_is_pad
            losses = losses * in_episode_bound.unsqueeze(-1)
            loss_dict["losses_after_in_ep_bound"] = losses.clone()

        # Remove padding
        # print(losses.shape)
        losses = losses[:, :, : self.config.max_action_dim]
        loss_dict["losses_after_rm_padding"] = losses.clone()

        if(~batch['is_vl_data']).any():
            # bsize, chunk, action_shape = losses.shape
            # vl_mask = (~batch['is_vl_data']).unsqueeze(1).unsqueeze(1).expand(bsize, chunk, action_shape)
            # loss = torch.sum(losses * vl_mask.to(dtype=torch.float16)) / (torch.sum(vl_mask))
            # TODO torch.sum(losses * vl_mask.to(dtype=torch.float16)) / (torch.sum(vl_mask)) != losses[~batch['is_vl_data']].mean()
            loss = losses[~batch['is_vl_data']].mean()
            # loss = losses.mean()
        else:
            loss = losses.mean() * 0.0
        # For backward pass
        loss_dict["loss"] = loss
        # For logging
        loss_dict["l2_loss"] = loss.item()

        if loss_lm is not None:
            ret_loss = {'loss': self.config.action_loss_weight * loss + loss_lm,
                    'llm_loss': loss_lm,
                    'action_loss': loss}
        else:
            ret_loss = {'loss': loss,
                    'llm_loss': (torch.ones(1) * (-100)).to(torch.bfloat16).squeeze(0),
                    'action_loss': loss}

        return {'loss': ret_loss}

    def prepare_images(self, batch):
        """Apply Pi0 preprocessing to the images, like resizing to 224x224 and padding to keep aspect ratio, and
        convert pixel range from [0.0, 1.0] to [-1.0, 1.0] as requested by SigLIP.
        """
        images = []
        img_masks = []

        present_img_keys = [key for key in self.config.image_features if key in batch]
        # present_img_keys = ['observation.images.cam_high', 'observation.images.cam_left_wrist', 'observation.images.cam_right_wrist']
        missing_img_keys = [key for key in self.config.image_features if key not in batch]

        if len(present_img_keys) == 0:
            raise ValueError(
                f"All image features are missing from the batch. At least one expected. (batch: {batch.keys()}) (image_features:{self.config.image_features})"
            )

        # Preprocess image features present in the batch
        for idx, key in enumerate(present_img_keys):
            img = batch[key]

            if self.config.resize_imgs_with_padding is not None:
                img = resize_with_pad(img, *self.config.resize_imgs_with_padding, pad_value=0)

            # Normalize from range [0,1] to [-1,1] as expacted by siglip
            img = img * 2.0 - 1.0

            bsize = img.shape[0]
            device = img.device
            mask = torch.ones(bsize, dtype=torch.bool, device=device)
            images.append(img)
            if idx != 0 and (batch['is_vl_data'].any()):
                # mask[batch['is_vl_data']] = torch.zeros(bsize, dtype=torch.bool, device=device)[batch['is_vl_data']]
                mask = (~batch['is_vl_data']).to(dtype=torch.bool, device=device)
                pass
            img_masks.append(mask)

        # Create image features not present in the batch
        # as fully 0 padded images.
        for num_empty_cameras in range(len(missing_img_keys)):
            if num_empty_cameras >= self.config.empty_cameras:
                break
            img = torch.ones_like(img) * -1
            mask = torch.zeros_like(mask)
            images.append(img)
            img_masks.append(mask)

        return images, img_masks

    def prepare_language(self, batch, eval=False) -> tuple[Any, Any, Any | None]:
        """Tokenize the text input"""
        device = batch[OBS_ROBOT].device
        tasks = batch["task"]

        # PaliGemma prompt has to end with a new line
        tasks = [task if task.endswith("\n") else f"{task}\n" for task in tasks]
        tasks = [self.language_tokenizer.bos_token + each for each in tasks]

        if getattr(self.config, "generate_reasoning", False): # prepare for generating reasoning languages
            reasonings = batch["reasoning"]
            if not eval:
                reasonings = [sfx + self.language_tokenizer.eos_token for sfx in reasonings]
                padding = "max_length"
            else:
                reasonings = None
                padding = "longest"

            return_token_type_ids = True
            tokenized_prompt = self.language_tokenizer.__call__(
                tasks,
                padding=padding,
                text_pair=reasonings,
                padding_side="right",
                max_length=self.config.tokenizer_max_length,
                return_token_type_ids=return_token_type_ids,
                return_tensors="pt",
                truncation=True,
                add_special_tokens=False,
            )
            labels = tokenized_prompt["input_ids"].masked_fill(tokenized_prompt["token_type_ids"] == 0, -100)
            if eval:
                assert 0 not in tokenized_prompt["input_ids"], "Do not padding the sequence during inference."
                labels = None

        else:
            tokenized_prompt = self.language_tokenizer.__call__(
                tasks,
                padding="max_length",
                padding_side="right",
                max_length=self.config.tokenizer_max_length,
                return_tensors="pt",
            )
            labels = None
        lang_tokens = tokenized_prompt["input_ids"].to(device=device)
        lang_masks = tokenized_prompt["attention_mask"].to(device=device, dtype=torch.bool)

        return lang_tokens, lang_masks, labels

    def prepare_language_with_action_tokens(self, batch, eval=False) -> tuple[Any, Any, Any | None]:
        """Tokenize the text input and actions input"""
        self._fast_skip_tokens = 128
        device = batch[OBS_ROBOT].device
        tasks = batch["task"]

        # PaliGemma prompt has to end with a new line
        tasks = [task if task.endswith("\n") else f"{task}\n" for task in tasks]
        tasks = [self.language_tokenizer.bos_token + each for each in tasks]

        reasonings = batch["reasoning"]

        action_tokens = self.fast_action_tokenizer(batch['action'].to(torch.float32).cpu().numpy())

        return_token_type_ids = True
        tokenized_prompt = self.language_tokenizer.__call__(
            tasks,
            text_pair=reasonings,
            return_token_type_ids=return_token_type_ids,
        )
        lang_action_tokens = []
        token_type_ids = []
        lang_masks = []
        idx = 0
        is_vl_data = batch['is_vl_data']
        for a_t, l_t in zip(action_tokens, tokenized_prompt["input_ids"]):
            if is_vl_data[idx]:
                action_tokens = (self.config.vocab_size - 1 - self._fast_skip_tokens - np.array(a_t)).tolist() + self.language_tokenizer.encode("|")[1:]
                len_action_tokens = len(action_tokens)
            else:
                action_tokens = []
                len_action_tokens = 0

            lang_action_tokens.append(torch.tensor(l_t + action_tokens + [self.language_tokenizer.eos_token_id]))
            token_type_ids.append(torch.tensor(tokenized_prompt["token_type_ids"][idx] + [1] * (len_action_tokens + 1)))
            lang_masks.append(torch.tensor(tokenized_prompt['attention_mask'][idx] + [1] * (len_action_tokens + 1)))
            idx += 1

        lang_action_tokens = pad_sequence(lang_action_tokens, batch_first=True, padding_value=0, padding_side="right")
        token_type_ids = pad_sequence(token_type_ids, batch_first=True, padding_value=0, padding_side="right")
        lang_masks = pad_sequence(lang_masks, batch_first=True, padding_value=0, padding_side="right")
        labels = lang_action_tokens.masked_fill(token_type_ids == 0, -100)

        if eval:
            labels = None

        lang_action_tokens = lang_action_tokens.to(device=device)
        lang_masks = lang_masks.to(device=device, dtype=torch.bool)

        return lang_action_tokens, lang_masks, labels

    def _pi_aloha_decode_state(self, state):
        # Flip the joints.
        for motor_idx in [1, 2, 8, 9]:
            state[:, motor_idx] *= -1
        # Reverse the gripper transformation that is being applied by the Aloha runtime.
        for motor_idx in [6, 13]:
            state[:, motor_idx] = aloha_gripper_to_angular(state[:, motor_idx])
        return state

    def _pi_aloha_encode_actions(self, actions):
        # Flip the joints.
        for motor_idx in [1, 2, 8, 9]:
            actions[:, :, motor_idx] *= -1
        # Reverse the gripper transformation that is being applied by the Aloha runtime.
        for motor_idx in [6, 13]:
            actions[:, :, motor_idx] = aloha_gripper_from_angular(actions[:, :, motor_idx])
        return actions

    def _pi_aloha_encode_actions_inv(self, actions):
        # Flip the joints again.
        for motor_idx in [1, 2, 8, 9]:
            actions[:, :, motor_idx] *= -1
        # Reverse the gripper transformation that is being applied by the Aloha runtime.
        for motor_idx in [6, 13]:
            actions[:, :, motor_idx] = aloha_gripper_from_angular_inv(actions[:, :, motor_idx])
        return actions

    def prepare_state(self, batch):
        """Pad state"""
        state = pad_vector(batch[OBS_ROBOT], self.config.max_state_dim)
        return state

    def prepare_action(self, batch):
        """Pad action"""
        actions = pad_vector(batch[ACTION], self.config.max_action_dim)
        return actions


class MIRROS2FlowMatching(PreTrainedModel):
    """

    ┌──────────────────────────────┐
    │               actions        │
    │               ▲              │
    │              ┌┴─────┐        │
    │  kv cache    │Gemma │        │
    │  ┌──────────►│Expert│        │
    │  │           │      │        │
    │ ┌┴────────┐  │x 10  │        │
    │ │         │  └▲──▲──┘        │
    │ │PaliGemma│   │  │           │
    │ │         │   │  robot state │
    │ │         │   noise          │
    │ └▲──▲─────┘                  │
    │  │  │                        │
    │  │  image(s)                 │
    │  language tokens             │
    └──────────────────────────────┘
    """

    def __init__(self, config):
        super().__init__(config)
        self.config = config
        self.state_repr = config.state_repr
        self.mask_strategy = config.mask_strategy
        self.paligemma_with_export_config = config.paligemma_with_export_config
        self.use_adaptive_rms_norm = config.use_adaptive_rms_norm
        if isinstance(config.paligemma_with_export_config, dict):
            cfg = PaliGemmaWithExpertConfig(**config.paligemma_with_export_config)
            self.paligemma_with_expert = PaliGemmaWithExpertModel(cfg)
        else:
            self.paligemma_with_expert = PaliGemmaWithExpertModel(config.paligemma_with_export_config)

        # Projections are float32
        if config.state_repr == "continuous":
            self.state_proj = nn.Linear(self.config.max_state_dim, self.config.proj_width)
            self.set_requires_grad()
        self.action_in_proj = nn.Linear(self.config.max_action_dim, self.config.proj_width)
        self.action_out_proj = nn.Linear(self.config.proj_width, self.config.max_action_dim)

        if self.use_adaptive_rms_norm:
            self.time_mlp_in = nn.Linear(self.config.proj_width, self.config.proj_width)
            self.time_mlp_out = nn.Linear(self.config.proj_width, self.config.proj_width)
        else:
            self.action_time_mlp_in = nn.Linear(self.config.proj_width * 2, self.config.proj_width)
            self.action_time_mlp_out = nn.Linear(self.config.proj_width, self.config.proj_width)


    def set_requires_grad(self):
        for params in self.state_proj.parameters():
            params.requires_grad = self.config.train_state_proj

    def sample_noise(self, shape, device):
        noise = torch.normal(
            mean=0.0,
            std=1.0,
            size=shape,
            dtype=torch.float32,
            device=device,
        )
        return noise

    def sample_time(self, bsize, device):
        time_beta = sample_beta(1.5, 1.0, bsize, device)
        time = time_beta * 0.999 + 0.001
        return time.to(dtype=torch.float32, device=device)

    def embed_prefix(
        self, images, img_masks, lang_tokens, lang_masks
    ) -> tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """Embed images with SigLIP and language tokens with embedding layer to prepare
        for PaliGemma transformer processing.
        """
        # TODO: avoid list in python and torch.cat ; prefer pre-allocation with torch.empty
        embs = []
        pad_masks = []
        att_masks = []

        # TODO: remove for loop
        for (
            img,
            img_mask,
        ) in zip(images, img_masks, strict=False):
            img_emb = self.paligemma_with_expert.embed_image(img)
            img_emb = img_emb.to(dtype=torch.bfloat16)

            # Normalize image embeddings
            img_emb_dim = img_emb.shape[-1]
            img_emb = img_emb * torch.tensor(img_emb_dim**0.5, dtype=img_emb.dtype, device=img_emb.device)

            bsize, num_img_embs = img_emb.shape[:2]
            img_mask = img_mask[:, None].expand(bsize, num_img_embs)

            embs.append(img_emb)
            pad_masks.append(img_mask)

            # Create attention masks so that image tokens attend to each other
            att_masks += [0] * num_img_embs

        lang_emb = self.paligemma_with_expert.embed_language_tokens(lang_tokens)

        # Normalize language embeddings
        lang_emb_dim = lang_emb.shape[-1]
        lang_emb = lang_emb * math.sqrt(lang_emb_dim)

        embs.append(lang_emb)
        pad_masks.append(lang_masks)

        # full attention between image and language inputs
        num_lang_embs = lang_emb.shape[1]
        att_masks += [0] * num_lang_embs

        embs = torch.cat(embs, dim=1)
        pad_masks = torch.cat(pad_masks, dim=1)
        att_masks = torch.tensor(att_masks, dtype=torch.bool, device=pad_masks.device)
        att_masks = att_masks[None, :].expand(bsize, len(att_masks))

        return embs, pad_masks, att_masks

    def embed_suffix(self, state, noisy_actions, timestep):
        """Embed state, noisy_actions, timestep to prepare for Expert Gemma processing."""
        embs = []
        pad_masks = []
        att_masks = []
        dtype = torch.bfloat16
        device = state.device
        # Embed state
        if self.state_repr == 'continuous':
            state_emb = self.state_proj(state.to(self.dtype))
            state_emb = state_emb.to(dtype=torch.bfloat16)
            embs.append(state_emb[:, None, :])
            bsize = state_emb.shape[0]


            state_mask = torch.ones(bsize, 1, dtype=torch.bool, device=device)
            pad_masks.append(state_mask)

            # Set attention masks so that image and language inputs do not attend to state or actions
            att_masks += [1]

        # Embed timestep using sine-cosine positional encoding with sensitivity in the range [0, 1]
        time_emb = create_sinusoidal_pos_embedding(
            timestep, self.config.proj_width, min_period=4e-3, max_period=4.0, device=device
        )
        time_emb = time_emb.type(dtype=dtype)

        # print(f"Inside the PI0FlowMatching embed_suffix: Default: {dtype}")
        # print(noisy_actions.dtype, self.action_in_proj.weight.dtype)
        noisy_actions = noisy_actions.to(dtype=torch.float32)
        self.action_in_proj.to(dtype=torch.float32)
        # Fuse timestep + action information using an MLP
        action_emb = self.action_in_proj(noisy_actions)
        action_emb = action_emb.to(dtype=dtype)

        if self.use_adaptive_rms_norm:
            # time MLP (for adaRMS)
            time_emb = self.time_mlp_in(time_emb)
            time_emb = F.silu(time_emb)
            time_emb = self.time_mlp_out(time_emb)
            time_emb = F.silu(time_emb)
            action_expert_tokens = action_emb
            adarms_cond = time_emb
        else:
            time_emb = time_emb[:, None, :].expand_as(action_emb)
            action_time_emb = torch.cat([action_emb, time_emb], dim=2)
            action_time_emb = action_time_emb.float()

            action_time_emb = self.action_time_mlp_in(action_time_emb.to(self.dtype))
            action_time_emb = F.silu(action_time_emb)  # swish == silu
            action_time_emb = self.action_time_mlp_out(action_time_emb)
            action_expert_tokens = action_time_emb
            adarms_cond = None

        # Add to input tokens
        embs.append(action_expert_tokens)

        bsize, action_time_dim = action_expert_tokens.shape[:2]
        action_time_mask = torch.ones(bsize, action_time_dim, dtype=torch.bool, device=device)
        pad_masks.append(action_time_mask)

        # Set attention masks so that image, language and state inputs do not attend to action tokens
        att_masks += [1] + ([0] * (self.config.n_action_steps - 1))

        embs = torch.cat(embs, dim=1)
        pad_masks = torch.cat(pad_masks, dim=1)
        att_masks = torch.tensor(att_masks, dtype=embs.dtype, device=embs.device)
        att_masks = att_masks[None, :].expand(bsize, len(att_masks))

        return embs, pad_masks, att_masks, adarms_cond

    def forward(
        self, images, img_masks, lang_tokens, lang_masks, state, actions, noise=None, time=None, labels=None
    ) -> Tensor:
        """Do a full training forward pass and compute the loss (batch_size x num_steps x num_motors)"""
        loss_action, loss_lm = self.flow_matching_forward(images=images, img_masks=img_masks, lang_tokens=lang_tokens,
                                                          lang_masks=lang_masks, state=state, actions=actions, noise=noise,
                                                          time=time, labels=labels)

        return loss_action, loss_lm

    def lm_forward(self, hidden_states, labels, attention_mask):
        logits = None
        loss = None
        if labels is not None:
            # Upcast to float if we need to compute the loss to avoid potential precision issues
            logits = self.paligemma_with_expert.paligemma.language_model.lm_head(hidden_states)
            logits = logits.float()
            shift_logits = logits[..., :-1, :]
            shift_labels = labels[..., 1:]
            if attention_mask is not None:
                # we use the input attention mask to shift the logits and labels, because it is 2D.
                # we also crop attn mask in case it is longer, which happens in PrefixTuning with peft
                shift_attention_mask = attention_mask[:, -shift_logits.shape[1]:].to(logits.device)
                shift_logits = shift_logits[shift_attention_mask.to(logits.device) != 0].contiguous()
                shift_labels = shift_labels[shift_attention_mask.to(shift_labels.device) != 0].contiguous()
            else:
                shift_logits = shift_logits.contiguous()
                shift_labels = shift_labels.contiguous()
            # Flatten the tokens
            loss_fct = nn.CrossEntropyLoss()

            flat_logits = shift_logits.view(-1, self.paligemma_with_export_config.paligemma_config._vocab_size)
            flat_labels = shift_labels.view(-1).to(shift_logits.device)
            loss = loss_fct(flat_logits, flat_labels)
        return (loss, logits)

    def flow_matching_forward(self, images, img_masks, lang_tokens, lang_masks, state, actions, noise=None, time=None, labels=None):
        if noise is None:
            noise = self.sample_noise(actions.shape, actions.device)

        if time is None:
            time = self.sample_time(actions.shape[0], actions.device)

        time_expanded = time[:, None, None]
        x_t = time_expanded * noise + (1 - time_expanded) * actions
        u_t = noise - actions

        prefix_embs, prefix_pad_masks, prefix_att_masks = self.embed_prefix(
            images, img_masks, lang_tokens, lang_masks
        )
        suffix_embs, suffix_pad_masks, suffix_att_masks, adarms_cond = self.embed_suffix(state, x_t, time)

        pad_masks = torch.cat([prefix_pad_masks, suffix_pad_masks], dim=1)
        att_masks = torch.cat([prefix_att_masks, suffix_att_masks], dim=1)

        if labels is not None: # add casual mask for reasoning language generation
            b, seq_l = prefix_pad_masks.shape

            labels = torch.cat([-100 * torch.ones((b, seq_l - labels.shape[1])), labels], dim=1).to(labels.dtype) # for calculating CE loss
            labels_expanded = torch.cat([labels, -100 * torch.ones_like(suffix_pad_masks).to('cpu')], dim=1).to(labels.dtype) # for generate partial casual mask
            att_masks = torch.where(
                -100 != labels_expanded.to(device=att_masks.device),
                torch.ones_like(att_masks).to(dtype=labels.dtype, device=att_masks.device),
                att_masks)

        att_2d_masks = make_att_2d_masks(pad_masks, att_masks)
        if self.mask_strategy == 'reason':
            start_idx = suffix_pad_masks.shape[-1]
            b, l = labels_expanded.shape
            action_mask = torch.arange(l).unsqueeze(0).repeat(b, 1)
            action_mask = action_mask > l - start_idx - 1
            gen_token_mask = labels_expanded != -100

            mask = action_mask.unsqueeze(2) & gen_token_mask.unsqueeze(1)
            att_2d_masks = att_2d_masks.masked_fill(mask.to(att_2d_masks.device), False)
            pass
        # elif self.mask_strategy == 'instruction':
        #     b, l = labels_expanded.shape
        #     start_idx_act = l - suffix_pad_masks.shape[-1]
        #     start_idx_ins = labels_expanded.shape[-1] - lang_tokens.shape[-1] - suffix_pad_masks.shape[-1] # start index of instruction
        #     end_idx_ins = torch.argmax((labels_expanded != -100).to(torch.int32), dim=-1, keepdim=True)
        #     seqlen = torch.arange(l).unsqueeze(0).repeat(b, 1)
        #     instruction_mask = (seqlen >= start_idx_ins) & (seqlen < end_idx_ins)
        #     action_mask = seqlen >= start_idx_act
        #     mask = action_mask.unsqueeze(2) & instruction_mask.unsqueeze(1)
        #     att_2d_masks = att_2d_masks.masked_fill(mask.to(att_2d_masks.device), False)
        #
        #     pass
        position_ids = torch.cumsum(pad_masks, dim=1) - 1

        (prefix_out, suffix_out), _ = self.paligemma_with_expert.forward(
            attention_mask=att_2d_masks,
            position_ids=position_ids,
            past_key_values=None,
            inputs_embeds=[prefix_embs, suffix_embs],
            use_cache=False,
            fill_kv_cache=False,
            adarms_cond=[None, adarms_cond],
        )
        len_suffix = suffix_pad_masks.shape[-1]
        loss_lm, _ = self.lm_forward(prefix_out, labels, pad_masks[:,:-len_suffix])

        suffix_out = suffix_out[:, -self.config.n_action_steps :]
        # Original openpi code, upcast attention output
        suffix_out = suffix_out.to(dtype=torch.float32)
        self.action_out_proj.to(dtype=torch.float32)
        v_t = self.action_out_proj(suffix_out)

        losses = F.mse_loss(u_t, v_t, reduction="none") # flow matching action loss
        return losses.to(dtype=torch.bfloat16), loss_lm

    def sample_actions(self, images, img_masks, lang_tokens, lang_masks, state, noise=None, generate_reasoning=False, language_tokenizer=None) -> Tensor:
        """Do a full inference forward and compute the action (batch_size x num_steps x num_motors)"""
        bsize = state.shape[0]
        device = state.device

        if noise is None:
            actions_shape = (bsize, self.config.n_action_steps, self.config.max_action_dim)
            noise = self.sample_noise(actions_shape, device)

        prefix_embs, prefix_pad_masks, prefix_att_masks = self.embed_prefix(
            images, img_masks, lang_tokens, lang_masks
        )
        prefix_att_2d_masks = make_att_2d_masks(prefix_pad_masks, prefix_att_masks)
        prefix_position_ids = torch.cumsum(prefix_pad_masks, dim=1) - 1

        # mirro forward
        if not generate_reasoning:
            # Compute image and language key value cache
            _, past_key_values = self.paligemma_with_expert.forward(
                attention_mask=prefix_att_2d_masks,
                position_ids=prefix_position_ids,
                past_key_values=None,
                inputs_embeds=[prefix_embs, None],
                use_cache=self.config.use_cache,
                fill_kv_cache=True,
            )
            outputs_text = None
            generate_len = None
        else:  # pi05 forward
            prefix_embs = prefix_embs / math.sqrt(
                prefix_embs.shape[-1])  # GemmaModel has already normalize the hidden states with emb dim
            outputs = self.paligemma_with_expert.paligemma.generate(inputs_embeds=prefix_embs,
                                                                    max_new_tokens=50,
                                                                    return_dict_in_generate=True,
                                                                    use_cache=True)
            outputs_text = language_tokenizer.batch_decode(outputs.sequences[:, :], skip_special_tokens=False)[0]
            generate_len = outputs.sequences.shape[1]
            print("Current Reasoning: " + outputs_text)
            past_key_values = []
            prefix_pad_masks = torch.ones((1, prefix_embs.shape[1] + outputs.sequences.shape[1] - 1), dtype=torch.bool,
                                          device='cuda')
            for each in outputs.past_key_values:  # transformers.cache_utils #L.474
                past_key_values.append(
                    dict(key_states=each[0].permute(0, 2, 1, 3), value_states=each[1].permute(0, 2, 1, 3)))

        dt = -1.0 / self.config.num_steps
        dt = torch.tensor(dt, dtype=torch.float32, device=device)

        x_t = noise
        time = torch.tensor(1.0, dtype=torch.float32, device=device)
        while time >= -dt / 2:
            expanded_time = time.expand(bsize)
            v_t = self.denoise_step(
                state,
                prefix_pad_masks,
                past_key_values,
                x_t,
                expanded_time,
                generate_len,
                lang_tokens.shape[-1],
            )

            # Euler step
            x_t += dt * v_t
            time += dt

        return x_t, outputs_text

    def denoise_step(
        self,
        state,
        prefix_pad_masks,
        past_key_values,
        x_t,
        timestep,
        generate_len=None,
        language_len=None
    ):
        """Apply one denoising step of the noise `x_t` at a given timestep."""
        suffix_embs, suffix_pad_masks, suffix_att_masks, adarms_cond = self.embed_suffix(state, x_t, timestep)

        suffix_len = suffix_pad_masks.shape[1]
        batch_size = prefix_pad_masks.shape[0]
        prefix_len = prefix_pad_masks.shape[1]
        prefix_pad_2d_masks = prefix_pad_masks[:, None, :].expand(batch_size, suffix_len, prefix_len)

        suffix_att_2d_masks = make_att_2d_masks(suffix_pad_masks, suffix_att_masks)

        full_att_2d_masks = torch.cat([prefix_pad_2d_masks, suffix_att_2d_masks], dim=2)
        if generate_len is not None:
            if self.mask_strategy == 'reason':
                start_idx = prefix_len - generate_len
                action_mask = torch.arange(suffix_len + prefix_len).unsqueeze(0)
                action_mask = (action_mask > start_idx) & (action_mask < start_idx + generate_len)

                mask = action_mask.unsqueeze(1).repeat(1, full_att_2d_masks.shape[1], 1)
                full_att_2d_masks = full_att_2d_masks.masked_fill(mask.to(full_att_2d_masks.device), False)
                pass
            # elif self.mask_strategy == 'instruction':
            #     # start index of instruction
            #     # The last token <eos> has no embedding, so the past kv cache lacks 1 embedding
            #     start_idx_ins = prefix_len - language_len - generate_len + 1
            #     end_idx_ins = prefix_len - generate_len + 1
            #     seqlen = torch.arange(suffix_len + prefix_len).unsqueeze(0)
            #     instruction_mask = (seqlen >= start_idx_ins) & (seqlen < end_idx_ins)
            #     mask = instruction_mask.unsqueeze(1).repeat(1, full_att_2d_masks.shape[1], 1)
            #     full_att_2d_masks = full_att_2d_masks.masked_fill(mask.to(full_att_2d_masks.device), False)

        prefix_offsets = torch.sum(prefix_pad_masks, dim=-1)[:, None]
        position_ids = prefix_offsets + torch.cumsum(suffix_pad_masks, dim=1) - 1

        outputs_embeds, _ = self.paligemma_with_expert.forward(
            attention_mask=full_att_2d_masks,
            position_ids=position_ids,
            past_key_values=past_key_values,
            inputs_embeds=[None, suffix_embs],
            adarms_cond=[None, adarms_cond],
            use_cache=self.config.use_cache,
            fill_kv_cache=False,
        )
        suffix_out = outputs_embeds[1]
        suffix_out = suffix_out[:, -self.config.n_action_steps :]
        suffix_out = suffix_out.to(dtype=torch.float32)
        v_t = self.action_out_proj(suffix_out)
        return v_t

AutoModel.register(MIRROS2Config, MIRROS2Policy)

if __name__ == "__main__":
    import pickle
    config = MIRROS2Config(generate_reasoning=True)
    camera_names = ["cam_left", "cam_right", "cam_high"]
    image_features = [f"observation.images.{cam}" for cam in camera_names]
    setattr(config, "image_features", image_features)

    model = MIRROS2Policy(config)
    model.to(torch.bfloat16)
    model.model.action_out_proj.to(torch.float32)
    with open("/home/<USER>/zhumj/data/local_debug_aloha/dataset_stats.pkl", 'rb') as f:
        stats = pickle.load(f)
    model.set_normalize(stats)
    model.to("cuda")
    bsz = 1
    model.language_tokenizer = AutoTokenizer.from_pretrained("/home/<USER>/zhumj/model_Param/paligemma-3b-pt-224-ori")
    fake_image = torch.randn((3, bsz, 3, 224, 224), dtype=torch.bfloat16, device="cuda")
    state = torch.randn(bsz, 14).to(dtype=torch.bfloat16, device="cuda")
    actions = torch.randn(bsz, 50, 14).to(dtype=torch.bfloat16, device="cuda")
    lang_embeddings = torch.randn(bsz, 768).to(dtype=torch.bfloat16, device="cuda")
    batch = {}
    for i, cam in enumerate(camera_names):
        batch[f"observation.images.{cam}"] = fake_image[i]
    batch['observation.state'] = state
    batch['lang_embeddings'] = lang_embeddings
    batch['action'] = actions
    batch['task'] = ["a set of code." for i in range(bsz)]
    batch['reasoning'] = ["Pick up the letter on the table." for i in range(bsz)]
    # x = model(batch)
    model.select_action(batch)
    model.reset()
    model.select_action(batch)
    print(model)
