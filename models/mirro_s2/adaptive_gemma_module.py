import torch.nn as nn
import torch


class AdaptiveGemmaRMSNorm(nn.Module):
    def __init__(self, dim: int, eps: float = 1e-6):
        super().__init__()
        self.eps = eps
        # self.weight = nn.Parameter(torch.zeros(dim))
        self.modulation = nn.Linear(dim, 3 * dim, bias=False)
        nn.init.zeros_(self.modulation.weight)
        # nn.init.zeros_(self.modulation.bias)

    def _norm(self, x):
        return x * torch.rsqrt(x.pow(2).mean(-1, keepdim=True) + self.eps)

    def forward(self, x, cond):
        output = self._norm(x.float())

        # if cond is None:
        #     # Llama does x.to(float16) * w whilst Gemma is (x * w).to(float16)
        #     # See https://github.com/huggingface/transformers/pull/29402
        #     output = output * (1.0 + self.weight.float())
        #     return output.type_as(x), None

        scale, shift, gate = torch.chunk(self.modulation(cond), 3, dim=-1)
        output = output * (1.0 + scale.float().unsqueeze(1)) + shift.float().unsqueeze(1)

        return output.type_as(x), gate.type_as(x)

    def extra_repr(self):
        return f"{tuple(self.weight.shape)}, eps={self.eps}"

def _gated_residual(x, y, gate):
    assert (x is None) == (y is None)
    if x is None:
        return None
    if gate is None:
        return x + y
    return x + y * gate.unsqueeze(1)