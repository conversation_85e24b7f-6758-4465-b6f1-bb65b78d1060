from transformers import PretrainedConfig, AutoConfig
from models.mirro_s1s2.paligemma_with_expert_moe import PaliGemmaWithExpertConfig
class MIRROS1S2Config(PretrainedConfig):
    model_type = "mirro_s1s2"
    def __init__(
            self,
            n_obs_steps: int = 1,
            chunk_size: int = 50,
            n_action_steps: int = 50,
            is_composition: bool = False,
            state_dim: int = 14,
            action_dim: int = 14,
            max_state_dim: int = 32,  # Shorter state and action vectors will be padded
            max_action_dim: int = 32,
            resize_imgs_with_padding: tuple[int, int] = (224, 224),  # Image preprocessing
            empty_cameras: int = 0,  # Add empty images. Used by pi0_aloha_sim which adds the empty
            adapt_to_pi_aloha: bool = False,  # Converts the joint and gripper values from the standard Aloha space to
            use_delta_joint_actions_aloha: bool = False,  # Converts joint dimensions to deltas with respect to the current state before passing to the model.
            tokenizer_max_length: int = 128,  # Tokenizer
            proj_width: int = 1024, # Projector
            num_steps: int = 10,  # Decoding
            use_cache: bool = True,  # Attention utils
            attention_implementation: str = "eager",  # or fa2, flex
            # Finetuning settings
            freeze_vision_encoder: bool = True,
            train_expert_only: bool = False,
            train_state_proj: bool = True,
            stop_gradient: bool = False,
            generate_reasoning: bool = False,
            routed_expert_num: int = 4,
            routed_top_k: int = 2,
            moe: bool = False,
            pretrained_path: str = None,
            image_features: list[str] = None,
            vocab_size: int = 257152,
            state_repr: str = "continuous",
            action_tokenizer: str = None,
            mask_stragety: str = None,
            action_loss_weight: float = 1.0,
            **kwargs
    ):
        self.n_obs_steps = n_obs_steps
        self.chunk_size = chunk_size
        self.n_action_steps = n_action_steps
        self.is_composition = is_composition
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.max_state_dim = max_state_dim
        self.max_action_dim = max_action_dim
        self.resize_imgs_with_padding = resize_imgs_with_padding
        self.empty_cameras = empty_cameras
        self.adapt_to_pi_aloha = adapt_to_pi_aloha
        self.use_delta_joint_actions_aloha = use_delta_joint_actions_aloha
        self.tokenizer_max_length = tokenizer_max_length

        self.proj_width = proj_width
        self.num_steps = num_steps
        self.use_cache = use_cache
        self.attention_implementation = attention_implementation
        self.freeze_vision_encoder = freeze_vision_encoder
        self.train_expert_only = train_expert_only
        self.train_state_proj = train_state_proj

        # custom
        self.stop_gradient = stop_gradient
        self.generate_reasoning = generate_reasoning
        self.pretrained_path = pretrained_path
        self.moe = moe
        self.routed_expert_num = routed_expert_num
        self.routed_top_k = routed_top_k
        self.image_features = image_features
        self.vocab_size = vocab_size
        self.state_repr = state_repr
        self.action_tokenizer = action_tokenizer
        self.mask_stragety = mask_stragety
        self.action_loss_weight = action_loss_weight

        if not self.moe:
            self.routed_expert_num = 0
            self.routed_top_k = 0

        self.paligemma_with_export_config = PaliGemmaWithExpertConfig(
            freeze_vision_encoder=freeze_vision_encoder,
            train_expert_only=train_expert_only,
            attention_implementation=attention_implementation,
            stop_gradient=stop_gradient,
            moe=moe,
            routed_expert_num=routed_expert_num,
            routed_top_k=routed_top_k,
        )

        super().__init__(**kwargs)

AutoConfig.register("mirro_s1s2", MIRROS1S2Config)