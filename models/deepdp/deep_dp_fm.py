import math
import torch

import torch.nn.functional as F
from typing import Optional, Union, List

from torch import nn, Tensor
from transformers import PreTrainedModel, PaliGemmaForConditionalGeneration, Cache

from models.utils.module import FilmBlock
from models.deepdp.configuration_deep_dp import DeepDPConfig

def apply_rope(x, positions, max_wavelength=10_000):
    """
    Applies RoPE positions [B, L] to x [B, L, H, D].
    """
    d_half = x.shape[-1] // 2
    device = x.device
    dtype = x.dtype
    x = x.to(torch.float32)

    freq_exponents = (2.0 / x.shape[-1]) * torch.arange(d_half, dtype=torch.float32, device=device)
    timescale = max_wavelength**freq_exponents
    radians = positions[..., None].to(torch.float32) / timescale[None, None, :].to(torch.float32)

    radians = radians[..., None, :]

    sin = torch.sin(radians)  # .to(dtype=dtype)
    cos = torch.cos(radians)  # .to(dtype=dtype)

    x1, x2 = x.split(d_half, dim=-1)
    res = torch.empty_like(x)
    res[..., :d_half] = x1 * cos - x2 * sin
    res[..., d_half:] = x2 * cos + x1 * sin

    return res.to(dtype)

# copied from lerobot.common.utils.utils
def get_safe_dtype(dtype: torch.dtype, device):
    """
    mps is currently not compatible with float64
    """
    if isinstance(device, torch.device):
        device = device.type
    if device == "mps" and dtype == torch.float64:
        return torch.float32
    else:
        return dtype

def create_sinusoidal_pos_embedding(
    time: torch.tensor, dimension: int, min_period: float, max_period: float, device="cpu"
) -> Tensor:
    """Computes sine-cosine positional embedding vectors for scalar positions."""
    if dimension % 2 != 0:
        raise ValueError(f"dimension ({dimension}) must be divisible by 2")

    if time.ndim != 1:
        raise ValueError("The time tensor is expected to be of shape `(batch_size, )`.")

    dtype = get_safe_dtype(torch.float64, device.type)
    fraction = torch.linspace(0.0, 1.0, dimension // 2, dtype=dtype, device=device)
    period = min_period * (max_period / min_period) ** fraction

    # Compute the outer product
    scaling_factor = 1.0 / period * 2 * math.pi
    sin_input = scaling_factor[None, :] * time[:, None]
    pos_emb = torch.cat([torch.sin(sin_input), torch.cos(sin_input)], dim=1)
    return pos_emb

def sample_beta(alpha, beta, bsize, device): # 1.5 1.0
    gamma1 = torch.empty((bsize,), device=device).uniform_(0, 1).pow(1 / alpha)
    gamma2 = torch.empty((bsize,), device=device).uniform_(0, 1).pow(1 / beta)
    return gamma1 / (gamma1 + gamma2)

def make_att_2d_masks(att_masks):
    """Copied from big_vision.

    Tokens can attend to valid inputs tokens which have a cumulative mask_ar
    smaller or equal to theirs. This way `mask_ar` int[B, N] can be used to
    setup several types of attention, for example:

      [[1 1 1 1 1 1]]: pure causal attention.

      [[0 0 0 1 1 1]]: prefix-lm attention. The first 3 tokens can attend between
          themselves and the last 3 tokens have a causal attention. The first
          entry could also be a 1 without changing behaviour.

      [[1 0 1 0 1 0 0 1 0 0]]: causal attention between 4 blocks. Tokens of a
          block can attend all previous blocks and all tokens on the same block.

    Args:
      input_mask: bool[B, N] true if its part of the input, false if padding.
      mask_ar: int32[B, N] mask that's 1 where previous tokens cannot depend on
        it and 0 where it shares the same attention mask as the previous token.
    """
    if att_masks.ndim != 2:
        raise ValueError(att_masks.ndim)

    cumsum = torch.cumsum(att_masks, dim=1)
    att_2d_masks = cumsum[:, None, :] <= cumsum[:, :, None]
    return att_2d_masks

class DeepDPFlowMatching(PreTrainedModel):
    config_class = DeepDPConfig
    supports_gradient_checkpointing = True
    _supports_sdpa = True  # * Make Transformers happy

    def __init__(self, config: DeepDPConfig):
        super().__init__(config)

        assert config.base_config is not None, "Base config should be initialized from paligemma Config"
        self.paligemma_for_deep_dp = PaliGemmaForConditionalGeneration(config=self.config.base_config)
        self.paligemma_for_deep_dp.language_model.lm_head = None
        self.paligemma_for_deep_dp.multi_modal_projector = None
        self.paligemma_for_deep_dp.language_model.model.embed_tokens = None

        if not self.vit_equal_act_hidden_state():
            self.projector_pre_attn_blocks = nn.ModuleList(
                [
                    nn.Linear(self.config.base_config.vision_config.hidden_size, self.config.base_config.text_config.hidden_size)
                    for _ in range(self.config.base_config.text_config.num_hidden_layers)
                ]
            )

            self.projector_post_attn_blocks = nn.ModuleList(
                [
                    nn.Linear(self.config.base_config.text_config.hidden_size, self.config.base_config.vision_config.hidden_size)
                    for _ in range(self.config.base_config.text_config.num_hidden_layers)
                ]
            )

        self.state_proj = nn.Linear(self.config.state_dim, self.config.base_config.hidden_size)
        self.action_in_proj = nn.Linear(self.config.action_dim, self.config.base_config.hidden_size)
        self.action_out_proj = nn.Linear(self.config.base_config.hidden_size, self.config.action_dim)

        self.action_time_mlp_in = nn.Linear(self.config.base_config.hidden_size * 2, self.config.base_config.hidden_size)
        self.action_time_mlp_out = nn.Linear(self.config.base_config.hidden_size, self.config.base_config.hidden_size)

        if self.config.oft:
            self.filmblocks = nn.ModuleList(
                [FilmBlock(self.config) for _ in range(self.config.base_config.text_config.num_hidden_layers)]
            )
        self.post_init()

    def vit_equal_act_hidden_state(self):
        return self.config.base_config.vision_config.hidden_size == self.config.base_config.text_config.hidden_size

    def sample_noise(self, shape, device, dtype):
        noise = torch.normal(
            mean=0.0,
            std=1.0,
            size=shape,
            dtype=dtype,
            device=device,
        )
        return noise

    def sample_time(self, bsize, device):
        time_beta = sample_beta(1.5, 1.0, bsize, device)
        time = time_beta * 0.999 + 0.001
        return time.to(dtype=torch.float32, device=device)

    def embed_prefix(self, images):
        """Embed images to patch embedding with SigLIP"""
        embs = []
        for img in images:
            img_emb = self.paligemma_for_deep_dp.vision_tower.vision_model.embeddings(img)
            # img_emb = img_emb * torch.tensor(img_emb_dim ** 0.5, dtype=img_emb.dtype, device=img_emb.device)
            embs.append(img_emb)

        # Create attention masks so that image tokens attend to each other
        embs = torch.cat(embs, dim=1)
        att_masks = torch.zeros(embs.shape[:2], device=embs.device)

        return embs, att_masks

    def embed_suffix(self, state, noisy_actions, timestep):
        """Embed state, noisy_actions, timestep to prepare for Expert Gemma processing."""
        embs = []
        att_masks = []

        # Embed state
        state_emb = self.state_proj(state)
        state_emb = state_emb.to(dtype=self.dtype)
        embs.append(state_emb[:, None, :])
        dtype = state_emb.dtype
        device = state_emb.device

        # Set attention masks so that image inputs do not attend to state or actions
        att_masks += [1]

        # Embed timestep using sine-cosine positional encoding with sensitivity in the range [0, 1]
        time_emb = create_sinusoidal_pos_embedding(
            timestep, self.config.base_config.hidden_size, min_period=4e-3, max_period=4.0, device=device
        )
        time_emb = time_emb.type(dtype=dtype)

        # Fuse timestep + action information using an MLP
        action_emb = self.action_in_proj(noisy_actions.to(dtype=self.dtype))

        time_emb = time_emb[:, None, :].expand_as(action_emb)
        action_time_emb = torch.cat([action_emb, time_emb], dim=2)

        action_time_emb = self.action_time_mlp_in(action_time_emb.to(self.dtype))
        action_time_emb = F.silu(action_time_emb)  # swish == silu
        action_time_emb = self.action_time_mlp_out(action_time_emb)

        # Add to input tokens
        embs.append(action_time_emb)

        bsize, action_time_dim = action_time_emb.shape[:2]

        # Set attention masks so that image, language and state inputs do not attend to action tokens
        att_masks += [1] + ([0] * (self.config.chunk_size - 1))

        embs = torch.cat(embs, dim=1)
        att_masks = torch.tensor(att_masks, dtype=embs.dtype, device=embs.device)
        att_masks = att_masks[None, :].expand(bsize, len(att_masks))

        return embs, att_masks

    def deep_fusion_forward(
        self,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_values: Optional[Union[List[torch.FloatTensor], Cache]] = None,
        inputs_embeds: List[torch.FloatTensor] = None,
        use_cache: Optional[bool] = None,
        fill_kv_cache: Optional[bool] = None,
        lang_embeddings: Optional[torch.Tensor] = None,
    ):
        models = [self.paligemma_for_deep_dp.vision_tower.vision_model.encoder, self.paligemma_for_deep_dp.language_model.model]

        for hidden_states in inputs_embeds:
            # TODO this is very inefficient
            # dtype is always the same, batch size too (if > 1 len)
            # device could be trickier in multi gpu edge cases but that's it
            if hidden_states is None:
                continue
            batch_size = hidden_states.shape[0]

        num_layers = self.config.base_config.text_config.num_hidden_layers
        head_dim = self.config.base_config.text_config.head_dim
        for layer_idx in range(num_layers):
            query_states = []
            key_states = []
            value_states = []
            for i, hidden_states in enumerate(inputs_embeds):
                if hidden_states is None:
                    continue
                layer = models[i].layers[layer_idx]

                # layer norm
                if i == 0:
                    hidden_states = layer.layer_norm1(hidden_states)
                else:
                    hidden_states = layer.input_layernorm(hidden_states)

                # q k v
                input_shape = hidden_states.shape[:-1]
                hidden_shape = (*input_shape, -1, layer.self_attn.head_dim)
                query_state = layer.self_attn.q_proj(hidden_states).view(hidden_shape)
                key_state = layer.self_attn.k_proj(hidden_states).view(hidden_shape)
                value_state = layer.self_attn.v_proj(hidden_states).view(hidden_shape)

                if i == 0 and not self.vit_equal_act_hidden_state():
                    query_state = query_state.view(*input_shape, -1)
                    key_state = key_state.view(*input_shape, -1)
                    value_state = value_state.view(*input_shape, -1)
                    query_state = self.projector_pre_attn_blocks[layer_idx](query_state)
                    key_state = self.projector_pre_attn_blocks[layer_idx](key_state)
                    value_state = self.projector_pre_attn_blocks[layer_idx](value_state)
                    query_state = query_state.view(*input_shape, -1, self.config.base_config.text_config.head_dim)
                    key_state = key_state.view(*input_shape, -1, self.config.base_config.text_config.head_dim)
                    value_state = value_state.view(*input_shape, -1, self.config.base_config.text_config.head_dim)

                if i == 1:
                    num_key_value_groups = self.config.base_config.text_config.num_attention_heads // self.config.base_config.text_config.num_key_value_heads
                    key_state = key_state[:, :, :, None, :].expand(*key_state.shape[:3], num_key_value_groups, head_dim).reshape(*key_state.shape[:2], -1, head_dim)
                    value_state = value_state[:, :, :, None, :].expand(*value_state.shape[:3], num_key_value_groups, head_dim).reshape(*value_state.shape[:2], -1, head_dim)

                query_states.append(query_state)
                key_states.append(key_state)
                value_states.append(value_state)

            # B,L,H,D with L sequence length, H number of heads, D head dim
            # concatenate on the number of embeddings/tokens
            query_states = torch.cat(query_states, dim=1)
            key_states = torch.cat(key_states, dim=1)
            value_states = torch.cat(value_states, dim=1)

            query_states = apply_rope(query_states, position_ids)
            key_states = apply_rope(key_states, position_ids)

            if use_cache and past_key_values is None:
                past_key_values = {}

            if use_cache:
                if fill_kv_cache:
                    past_key_values[layer_idx] = {
                        "key_states": key_states,
                        "value_states": value_states,
                    }
                else:
                    # TODO here, some optimization can be done - similar to a `StaticCache` we can declare the `max_len` before.
                    # so we create an empty cache, with just one cuda malloc, and if (in autoregressive case) we reach
                    # the max len, then we (for instance) double the cache size. This implementation already exists
                    # in `transformers`. (molbap)
                    key_states = torch.cat([past_key_values[layer_idx]["key_states"], key_states], dim=1)
                    value_states = torch.cat(
                        [past_key_values[layer_idx]["value_states"], value_states], dim=1
                    )

            att_output = self.eager_attention_forward(
                attention_mask, batch_size, head_dim, query_states, key_states, value_states
            )
            att_output = att_output.to(dtype=self.dtype)

            # first part of att_output is prefix (up to sequence length, [:, 0:prefix_seq_len])
            outputs_embeds = []
            start = 0
            for i, hidden_states in enumerate(inputs_embeds):
                layer = models[i].layers[layer_idx]
                if hidden_states is not None:
                    end = start + hidden_states.shape[1]

                    if i == 0:
                        if not self.vit_equal_act_hidden_state():
                            out_emb = layer.self_attn.out_proj(
                                self.projector_post_attn_blocks[layer_idx](att_output[:, start:end])
                            )
                        else:
                            out_emb = layer.self_attn.out_proj(att_output[:, start:end])
                    else:
                        out_emb = layer.self_attn.o_proj(att_output[:, start:end])

                    # TODO: first dropout (by default 0.0)

                    # first residual
                    out_emb += hidden_states

                    # OpenVLA-oft like film block to fusion language embedding into the vision embeddings
                    if i == 0 and self.config.oft:
                        out_emb = self.filmblocks[layer_idx](out_emb, lang_embeddings)

                    after_first_residual = out_emb.clone()

                    if i == 0:
                        out_emb = layer.layer_norm2(hidden_states)
                    else:
                        out_emb = layer.post_attention_layernorm(out_emb)
                    out_emb = layer.mlp(out_emb)

                    # TODO: second dropout (by default 0.0)

                    # second residual
                    out_emb += after_first_residual

                    outputs_embeds.append(out_emb)

                    start = end
                else:
                    outputs_embeds.append(None)

            inputs_embeds = outputs_embeds

        # final norm
        outputs_embeds = []
        for i, hidden_states in enumerate(inputs_embeds):
            if hidden_states is not None:
                if i == 0:
                    out_emb = self.paligemma_for_deep_dp.vision_tower.vision_model.post_layernorm(hidden_states)
                else:
                    out_emb = models[i].norm(hidden_states)
                outputs_embeds.append(out_emb)
            else:
                outputs_embeds.append(None)

        return outputs_embeds, past_key_values

    def eager_attention_forward(
            self, attention_mask,batch_size, head_dim, query_states, key_states, value_states
    ):
        num_att_heads = self.config.base_config.text_config.num_attention_heads
        num_key_value_heads = self.config.base_config.text_config.num_key_value_heads
        num_key_value_groups = num_att_heads // num_key_value_heads

        # query_states: batch_size, sequence_length, num_att_head, head_dim
        # key_states: batch_size, sequence_length, num_key_value_head, head_dim
        # value_states: batch_size, sequence_length, num_key_value_head, head_dim

        # Attention here is upcasted to float32 to match the original eager implementation.
        query_states = query_states.to(dtype=torch.float32)
        key_states = key_states.to(dtype=torch.float32)

        query_states = query_states.transpose(1, 2)
        key_states = key_states.transpose(1, 2)

        att_weights = torch.matmul(query_states, key_states.transpose(2, 3))
        att_weights *= head_dim ** -0.5
        big_neg = -2.3819763e38  # See gemma/modules.py

        masked_att_weights = torch.where(attention_mask[:, None, :, :], att_weights, big_neg)

        probs = nn.functional.softmax(masked_att_weights, dim=-1)
        probs = probs.to(dtype=value_states.dtype)

        # probs: batch_size, num_key_value_head, num_att_head, sequence_length, sequence_length
        # value_states: batch_size, sequence_length, num_att_heads, head_dim

        att_output = torch.matmul(probs, value_states.permute(0, 2, 1, 3))

        att_output = att_output.permute(0, 2, 1, 3)
        # we use -1 because sequence length can change
        att_output = att_output.reshape(batch_size, -1, num_key_value_heads * num_key_value_groups * head_dim)

        return att_output

    def forward(
        self, images, state, actions, lang_embeddings=None, noise=None, time=None
    ) -> Tensor:
        """Do a full training forward pass and compute the loss (batch_size x num_steps x num_motors)"""
        if noise is None:
            noise = self.sample_noise(actions.shape, actions.device, actions.dtype)

        if time is None:
            time = self.sample_time(actions.shape[0], actions.device)

        time_expanded = time[:, None, None]
        x_t = time_expanded * noise + (1 - time_expanded) * actions
        u_t = noise - actions

        prefix_embs, prefix_att_masks = self.embed_prefix(images)
        suffix_embs, suffix_att_masks = self.embed_suffix(state, x_t, time)

        att_masks = torch.cat([prefix_att_masks, suffix_att_masks], dim=1)

        att_2d_masks = make_att_2d_masks(att_masks)
        position_ids = torch.arange(att_masks.shape[1], device=actions.device)[None, ...].expand(actions.shape[0], att_masks.shape[1])

        (_, suffix_out), _ = self.deep_fusion_forward(
            attention_mask=att_2d_masks,
            position_ids=position_ids,
            past_key_values=None,
            inputs_embeds=[prefix_embs, suffix_embs],
            use_cache=False,
            fill_kv_cache=False,
            lang_embeddings=lang_embeddings
        )
        suffix_out = suffix_out[:, -self.config.chunk_size:]
        # Original openpi code, upcast attention output
        # suffix_out = suffix_out.to(dtype=torch.float32)
        v_t = self.action_out_proj(suffix_out)

        losses = F.mse_loss(u_t, v_t, reduction="none")
        return losses.to(dtype=torch.bfloat16)

    def sample_actions(self, images, state, noise, lang_embeddings=None):
        """Do a full inference forward and compute the action (batch_size x num_steps x num_motors)"""
        bsize = state.shape[0]
        device = state.device
        if noise is None:
            actions_shape = (bsize, self.config.chunk_size, self.config.action_dim)
            noise = self.sample_noise(actions_shape, device, state.dtype)

        prefix_embs, prefix_att_masks = self.embed_prefix(images)
        prefix_att_2d_masks = make_att_2d_masks(prefix_att_masks)
        prefix_position_ids = torch.arange(prefix_embs.shape[1], device=device)[None, :].expand(bsize, prefix_embs.shape[1])

        # Compute image and language key value cache
        _, past_key_values = self.deep_fusion_forward(
            attention_mask=prefix_att_2d_masks,
            position_ids=prefix_position_ids,
            past_key_values=None,
            inputs_embeds=[prefix_embs, None],
            use_cache=True,
            fill_kv_cache=True,
            lang_embeddings=lang_embeddings
        )

        dt = -1.0 / self.config.num_steps
        dt = torch.tensor(dt, dtype=torch.float32, device=device)

        x_t = noise
        time = torch.tensor(1.0, dtype=torch.float32, device=device)
        while time >= -dt / 2:
            expanded_time = time.expand(bsize)
            v_t = self.denoise_step(
                state,
                prefix_att_masks,
                past_key_values,
                x_t,
                expanded_time,
            )

            # Euler step
            x_t += dt * v_t
            time += dt

        return x_t

    def denoise_step(self, state, prefix_att_masks, past_key_values, x_t, timestep):
        """Apply one denoising step of the noise `x_t` at a given timestep."""
        suffix_embs, suffix_att_masks = self.embed_suffix(state, x_t, timestep)

        suffix_len = suffix_att_masks.shape[1]
        batch_size = prefix_att_masks.shape[0]
        prefix_len = prefix_att_masks.shape[1]

        prefix_att_2d_masks = prefix_att_masks[:, None, :].expand(batch_size, suffix_len, prefix_len)
        prefix_att_2d_masks = 1 - prefix_att_2d_masks
        suffix_att_2d_masks = make_att_2d_masks(suffix_att_masks)

        full_att_2d_masks = torch.cat([prefix_att_2d_masks, suffix_att_2d_masks], dim=2).to(torch.bool)

        position_ids = torch.sum(1 - prefix_att_masks, dim=-1)[:, None] + torch.arange(suffix_embs.shape[1], device=state.device)

        outputs_embeds, _ = self.deep_fusion_forward(
            attention_mask=full_att_2d_masks,
            position_ids=position_ids,
            past_key_values=past_key_values,
            inputs_embeds=[None, suffix_embs],
            use_cache=True,
            fill_kv_cache=False,
        )
        suffix_out = outputs_embeds[1]
        suffix_out = suffix_out[:, -self.config.chunk_size:]
        suffix_out = suffix_out.to(dtype=torch.float32)
        v_t = self.action_out_proj(suffix_out)
        return v_t