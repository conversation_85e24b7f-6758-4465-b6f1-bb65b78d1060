import torch
from collections import deque

from torch import Tensor
import torch.nn.functional as F
from transformers import PreTrainedModel, AutoConfig, AutoModel

from models.utils.normalize import NormalizeMeanStd
from models.paligemma_s1.paligemma_s1_fm import PaliGemmaS1FlowMatching
from models.paligemma_s1.configuration_paligemma_s1 import PaliGemmaS1Config

def resize_with_pad(img, width, height, pad_value=-1):
    # assume no-op when width height fits already
    if img.ndim != 4:
        raise ValueError(f"(b,c,h,w) expected, but {img.shape}")

    cur_height, cur_width = img.shape[2:]

    ratio = max(cur_width / width, cur_height / height)
    resized_height = int(cur_height / ratio)
    resized_width = int(cur_width / ratio)
    resized_img = F.interpolate(
        img, size=(resized_height, resized_width), mode="bilinear", align_corners=False
    )

    pad_height = max(0, int(height - resized_height))
    pad_width = max(0, int(width - resized_width))

    # pad on left and top of image
    padded_img = F.pad(resized_img, (pad_width, 0, pad_height, 0), value=pad_value)
    return padded_img

class PaliGemmaS1Policy(PreTrainedModel):
    config_class = PaliGemmaS1Config
    supports_gradient_checkpointing = True
    name = "paligemma_s1"
    def __init__(
            self,
            config: PaliGemmaS1Config,
    ):
        super().__init__(config)
        self.model = PaliGemmaS1FlowMatching(config)
        self.reset()

    def set_normalize(self, dataset_stats):
        self.normalize_inputs = NormalizeMeanStd(dataset_stats['qpos_mean'], dataset_stats['qpos_std'])
        self.normalize_targets = NormalizeMeanStd(dataset_stats['action_mean'], dataset_stats['action_std'])

    def reset(self):
        """This should be called whenever the environment is reset."""
        self._action_queue = deque([], maxlen=self.config.chunk_size)

    def forward(self, batch: dict[str, Tensor], noise=None, time=None) -> dict[str, Tensor]:
        state = self.normalize_inputs(batch['observation.state'])
        actions = self.normalize_targets(batch['action'])
        images = self.prepare_images(batch)
        lang_embeddings = batch.get('lang_embeddings')
        if lang_embeddings is not None:
            lang_embeddings = lang_embeddings.to(self.dtype)
        actions_is_pad = batch.get("action_is_pad")

        loss_dict = {}
        losses = self.model.forward(images, state, actions, lang_embeddings, noise, time)
        loss_dict["losses_after_forward"] = losses.clone()

        if actions_is_pad is not None:
            in_episode_bound = 1-actions_is_pad
            losses = losses * in_episode_bound.unsqueeze(-1)
            loss_dict["losses_after_in_ep_bound"] = losses.clone()

        loss = losses.mean()
        # For backward pass
        loss_dict["loss"] = loss
        # For logging
        loss_dict["l2_loss"] = loss.item()

        ret_loss = {
            'loss': loss,
            'llm_loss': (torch.ones(1) * (-100)).to(loss.dtype).squeeze(0),
            'action_loss': loss
        }

        return {'loss': ret_loss}

    def prepare_images(self, batch):
        images = []

        present_img_keys = [key for key in self.config.image_features if key in batch]

        if len(present_img_keys) == 0:
            raise ValueError(
                f"All image features are missing from the batch. At least one expected. (batch: {batch.keys()}) (image_features:{self.config.image_features})"
            )

        # Preprocess image features present in the batch
        for key in present_img_keys:
            img = batch[key]

            if self.config.resize_imgs_with_padding is not None:
                img = resize_with_pad(img, *self.config.resize_imgs_with_padding, pad_value=0)

            # Normalize from range [0,1] to [-1,1] as expacted by siglip
            img = img * 2.0 - 1.0
            images.append(img)

        return images

    @torch.no_grad
    def select_action(self, batch: dict[str, Tensor], noise: Tensor | None = None) -> Tensor:
        """Select a single action given environment observations.

        This method wraps `select_actions` in order to return one action at a time for execution in the
        environment. It works by managing the actions in a queue and only calling `select_actions` when the
        queue is empty.
        """
        self.eval()

        state = self.normalize_inputs(batch['observation.state'])
        lang_embeddings = batch.get('lang_embeddings')
        if lang_embeddings is not None:
            lang_embeddings = lang_embeddings.to(self.dtype)

        # Action queue logic for n_action_steps > 1. When the action_queue is depleted, populate it by
        # querying the policy.
        if len(self._action_queue) == 0:
            images = self.prepare_images(batch)
            state = state
            # lang_tokens, lang_masks = self.prepare_language(batch)

            actions = self.model.sample_actions(
                images, state, noise=noise, lang_embeddings=lang_embeddings
            )

            # unnormalize actions
            actions = self.normalize_targets.unnormalize(actions)

            # `self.model.forward` returns a (batch_size, n_action_steps, action_dim) tensor, but the queue
            # effectively has shape (n_action_steps, batch_size, *), hence the transpose.
            self._action_queue.extend(actions.transpose(0, 1))
        return self._action_queue.popleft()

AutoModel.register(PaliGemmaS1Config, PaliGemmaS1Policy)

if __name__ == "__main__":
    import pickle
    config = AutoConfig.from_pretrained("/home/<USER>/zhumj/model_Param/paligemma-3b-pt-224-ori")
    config = PaliGemmaS1Config(action_dim=14, state_dim=14, chunk_size=50, oft=False, num_steps=10, base_config=config)
    camera_names = ["cam_left", "cam_right", "cam_high"]
    image_features = [f"observation.images.{cam}" for cam in camera_names]
    setattr(config, "image_features", image_features)

    model = PaliGemmaS1Policy(config)
    with open("/home/<USER>/zhumj/data/local_debug_aloha/dataset_stats.pkl", 'rb') as f:
        stats = pickle.load(f)
    model.set_normalize(stats)
    fake_image = torch.randn((3, 1, 3, 224, 224))
    state = torch.randn(1, 14)
    actions = torch.randn(1, 50, 14)
    lang_embeddings = torch.randn(1, 768)
    batch = {}
    for i, cam in enumerate(camera_names):
        batch[f"observation.images.{cam}"] = fake_image[i]
    batch['observation.state'] = state
    batch['lang_embeddings'] = lang_embeddings
    batch['action'] = actions

    # x = model(batch)
    model.select_action(batch)
    print(model)