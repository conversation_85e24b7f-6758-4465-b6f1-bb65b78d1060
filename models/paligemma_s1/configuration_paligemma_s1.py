from transformers import PretrainedConfig, AutoConfig, CONFIG_MAPPING

class PaliGemmaS1Config(PretrainedConfig):
    """
    Configuration for deep fusion diffusion model.
    """
    model_type = "paligemma_s1"

    def __init__(
            self,
            base_config = None,
            action_dim: int = 14,
            state_dim: int = 14,
            chunk_size: int = 50,
            num_steps: int = 10,
            resize_imgs_with_padding: tuple[int, int] = (224, 224),
            oft: bool = False,
            llm_dim: int = 768,
            **kwargs
    ):

        self.base_config = base_config

        # parameters for policy network
        self.action_dim = action_dim
        self.state_dim = state_dim
        self.chunk_size = chunk_size
        self.num_steps = num_steps
        self.resize_imgs_with_padding = resize_imgs_with_padding
        self.oft = oft
        self.llm_dim = llm_dim

        if isinstance(self.base_config, dict):
            print(f"init base config from {base_config['model_type']}")
            base_config["model_type"] = base_config["model_type"] if "model_type" in base_config else "paligemma"
            self.base_config = CONFIG_MAPPING[base_config["model_type"]](**base_config)
        elif base_config is None:
            self.base_config = CONFIG_MAPPING['paligemma']()

        super().__init__(**kwargs)

AutoConfig.register("paligemma_s1", PaliGemmaS1Config)