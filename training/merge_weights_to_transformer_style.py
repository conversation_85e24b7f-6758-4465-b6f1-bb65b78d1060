from training.utils import load_vla_model
from transformers import AutoTokenizer
if __name__ == '__main__':
    model_init_kwargs = dict(
        vla_model_name='mirro',
        pretrained_path="/media/rl/HDD/data/multi_head_train_results/lerobot_pi0_results/xdof_pretrain_pretrain_xdof_constant_1_epoch_300h/checkpoint-350000",
        # pretrained_path="/media/rl/HDD/data/weights/pi0_lerobot/pi0",
        moe=True,
    )
    save_path = "/media/rl/HDD/data/weights/mirro/mirro_s1s2_init_from_pi0"
    model = load_vla_model(model_init_kwargs)
    model.language_tokenizer = AutoTokenizer.from_pretrained(model_init_kwargs['pretrained_path'])
    model.save_pretrained(save_path)
    model.language_tokenizer.save_pretrained(save_path)
    print("Successfully saved model to transformer style.")