from transformers import AutoTokenizer, AutoModel
from data_utils.constants import RESET, RED
import os
import torch
import transformers
import torch.nn as nn
from training.config import ModelArguments, TrainingArguments, ActionHeadArguments, DataArguments

def rank0_print(*args):
    if local_rank == 0:
        print(*args)

def return_config_class_by_name(name):
    if name == 'mirro':
        from models.mirro.configuration_mirro import MIRROConfig
        return MIRROConfig
    elif name == 'mirro_s2':
        from models.mirro_s2.configuration_mirro_s2 import MIRROS2Config
        return MIRROS2Config
    elif name == 'mirro_s1s2':
        from models.mirro_s1s2.configuration_mirro_s1s2 import MIRROS1S2Config
        return MIRROS1S2Config
    else:
        raise ValueError(f'Unknown model class {name}')

def load_vla_model(model_init_kwargs, device='cpu', data_stats=None):
    vla_model_name = model_init_kwargs['vla_model_name']
    try:
        vocab_size = model_init_kwargs['vocab_size']
    except KeyError:
        vocab_size = 257152
    cfg = return_config_class_by_name(vla_model_name)(**model_init_kwargs)
    policy = AutoModel.from_config(cfg)
    policy.set_normalize(data_stats)

    init_from_paligemma = "paligemma" in cfg.pretrained_path if cfg.pretrained_path is not None else False
    if cfg.pretrained_path and not init_from_paligemma:
        print(f"Current model: {RED}{vla_model_name}{RESET}. Loading pretrained weights from {RED}official Pi0.{RESET} {cfg.pretrained_path}")
        # Load a pretrained policy and override the config if needed (for example, if there are inference-time
        # hyperparameters that we want to vary).
        from safetensors.torch import load_file
        files = os.listdir(cfg.pretrained_path)
        files = [f for f in files if f.endswith("safetensors")]
        all_weights = {}
        for file in files:
            _weights = load_file(os.path.join(cfg.pretrained_path, file))
            all_weights.update(_weights)
        all_weights = {k:v for k,v in all_weights.items() if 'normalize' not in k}
        policy.load_state_dict(all_weights, strict=False)

        if getattr(policy.config, "moe", False):
            mlp_weights_path = os.path.join(cfg.pretrained_path, "mlp.bin")
            params = torch.load(mlp_weights_path)
            loaded = {}
            for k, v in params.items():
                k = k.replace("language_model.model.layers.", '')
                for i in range(policy.config.routed_expert_num):
                    loaded[k.replace('mlp', 'routed_experts.experts.' + str(i))] = v
            policy.model.paligemma_with_expert.share_moe_expert.load_state_dict(loaded, strict=False)
            print(f"{RED}initialize moe weights from paligemma mlp{RESET}")
        if vocab_size > 257152:
            policy.model.paligemma_with_expert.paligemma.resize_token_embeddings(vocab_size)

    elif init_from_paligemma:
        print(f"Current model: {vla_model_name}. Loading pretrained weights from {RED}PaliGEMMA.{RESET} {cfg.pretrained_path}")
        from safetensors.torch import load_file
        files = os.listdir(cfg.pretrained_path)
        files = [f for f in files if f.endswith("safetensors")]
        all_weights = {}
        for file in files:
            _weights = load_file(os.path.join(cfg.pretrained_path, file))
            all_weights.update(_weights)
        pass
        old_weight = all_weights.pop("language_model.model.embed_tokens.weight") # shape: (257216, 2048)
        policy.model.paligemma_with_expert.paligemma.load_state_dict(all_weights, strict=False)

        #Todo vocab_size in paligemma > mirro
        policy.model.paligemma_with_expert.paligemma.language_model.model.embed_tokens.weight.data = old_weight[:vocab_size,:]  # shape: (257152, 2048)
        # policy.model.paligemma_with_expert.paligemma.language_model.lm_head.weight.data = old_weight[:257152,:]

    else:
        print(
            "You are instantiating a policy from scratch and its features are parsed from an environment "
            "rather than a dataset. Normalization modules inside the policy will have infinite values "
            "by default without stats from a dataset."
        )

    policy.to(device)
    assert isinstance(policy, nn.Module)

    # policy = torch.compile(policy, mode="reduce-overhead")
    policy.language_tokenizer = AutoTokenizer.from_pretrained(cfg.pretrained_path)
    policy.language_tokenizer.add_bos_token = False
    policy.language_tokenizer.add_eos_token = False
    return policy

def parse_param():
    global local_rank

    parser = transformers.HfArgumentParser(
        (ModelArguments, DataArguments, TrainingArguments, ActionHeadArguments))
    model_args, data_args, training_args, action_head_args = parser.parse_args_into_dataclasses()

    local_rank = training_args.local_rank
    compute_dtype = (torch.float16 if training_args.fp16 else (torch.bfloat16 if training_args.bf16 else torch.float32))

    assert not(data_args.use_reasoning_as_instructions and model_args.generate_reasoning), \
        "data_args.use_reasoning_as_instructions and model_args.generate_reasoning cannot be set true simultaneously."

    return model_args, data_args, training_args, action_head_args, local_rank
