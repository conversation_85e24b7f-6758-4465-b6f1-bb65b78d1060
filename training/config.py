import os
import transformers
from dataclasses import dataclass, field, asdict
from typing import Any, Dict, List, Optional

#  <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<parameters>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
@dataclass
class ActionHeadArguments:
    state_dim: int = 14
    action_dim: int = 14


@dataclass
class ModelArguments:
    diffusion_strategy: str = field(default="flow") # diffusion or flow
    pretrain_vlm_backbone_path: Optional[str] = field(default="None") # path to pretrained model
    pretrain_action_expert_path: Optional[str] = field(default=None) # path to pretrained dit weights
    generate_reasoning: bool = False #
    stop_gradient: bool = field(default=False)
    vla_model_name: str = field(default="mirro")

@dataclass
class DataArguments:
    use_reasoning_as_instructions: bool = False
    gen_reasoning: bool = False
    task_name: str = field(default="stack_cube_2024_6_2")
    skip_mirrored_data: bool = field(default=False)
    chunk_size: int = field(default=16)
    relative_control: bool = field(default=False) # the same as mirro https://github.com/Physical-Intelligence/openpi/blob/072217ef94d2ac8710a2113048a398bc4b69efec/src/openpi/training/config.py#L299
    resize_image_size: str = "(224,224)"  # default to 270 x 480
    annotation_dir: str="" # path to external annotation dir

@dataclass
class TrainingArguments(transformers.TrainingArguments):
    local_debug: bool = field(default=False)

    cache_dir: Optional[str] = field(default=None)
    optim: str = field(default="adamw_torch")
    adam_beta1: float = field(default=0.9)
    adam_beta2: float = field(default=0.98)
    adam_epsilon: float = field(default=1e-7)
    remove_unused_columns: bool = field(default=False)

    flash_attn: bool = field(default=False)

    freeze_vision_tower: bool = field(default=False)
    freeze_backbone: bool = field(default=False)
    tune_mm_mlp_adapter: bool = field(default=False)
    resume_from_checkpoint: bool = field(default=False)
    llm_loss_weight: float = field(default=1.0)

    seed: int = field(default=0)

    # logger
    logging_dir: str = field(default='./logs')  # TensorBoard日志的保存目录
    logging_strategy: str = field(default='steps')  # 设置为`steps`表示每几步记录一次日志
    logging_steps: int = field(default=10)

    save_steps: int = field(default=10)  # 每隔多少步保存一次模型
    num_train_epochs: int = field(default=3)
    max_steps: int = field(default=5000)

    # validate
    do_eval: bool = field(default=False)
    evaluation_strategy: str = field(default="no")
    eval_steps: int = field(default=200)
    per_device_eval_batch_size: int = field(default=32)

    dataloader_pin_memory: bool = False
    # lora
    lora_enable: bool = True
    lora_module: str = "vit"
    lora_task_type: str = 'CAUSAL_LM'
    lora_r: int = 64
    lora_alpha: int = 256
    lora_dropout: float = 0.05
    lora_weight_path: str = ""
    lora_bias: str = "none"
    non_lora_lr: Optional[float] = None
    group_by_modality_length: bool = field(default=False)
    attention_implementation: str='eager'
    model_max_length: int = field(
        default=2048,
        metadata={
            "help":
                "Maximum sequence length. Sequences will be right padded (and possibly truncated)."
        },
    )
    double_quant: bool = field(
        default=True,
        metadata={"help": "Compress the quantization statistics through double quantization."}
    )
    quant_type: str = field(
        default="nf4",
        metadata={"help": "Quantization data type to use. Should be one of `fp4` or `nf4`."}
    )
    bits: int = field(
        default=16,
        metadata={"help": "How many bits to use."}
    )


#  <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<parameters>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>