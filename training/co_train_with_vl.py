import os

os.environ["TOKENIZERS_PARALLELISM"] = "false"

os.environ['DEVICE'] = "cuda"
os.environ["WANDB_DISABLED"] = "true"
import transformers
from dataclasses import dataclass, field, asdict
from models import *
from config import DataArguments, ActionHeadArguments, TrainingArguments, ModelArguments
from utils import load_vla_model
from data_utils.constants import TASK_CONFIGS, RED, RESET
from data_utils.data_collator import PIOCollator
from data_utils.dataset_with_vl import load_data, set_seed
from models.mirro_trainer import MIRROTrainer
local_rank = None

#  >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>parameters<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<

@dataclass
class DataArguments(DataArguments):
    vl_data_json: str = None
    vl_image_dir: str = None
    vocab_size: int = 257216
    state_repr: str = 'text' # text / continuous
    action_tokenizer: str = None
    norm_mode: str = 'mean_std',

@dataclass
class ModelArguments(ModelArguments):
    mask_strategy: str = None # reason or instruction
    moe: bool = field(default=False)
    routed_expert_num: int = 4
    routed_top_k: int = 2
    action_loss_weight: int = 1
    use_adaptive_rms_norm: bool = False

def parse_param():
    global local_rank

    parser = transformers.HfArgumentParser(
        (ModelArguments, DataArguments, TrainingArguments, ActionHeadArguments))
    model_args, data_args, training_args, action_head_args = parser.parse_args_into_dataclasses()

    local_rank = training_args.local_rank
    compute_dtype = (torch.float16 if training_args.fp16 else (torch.bfloat16 if training_args.bf16 else torch.float32))

    assert not(data_args.use_reasoning_as_instructions and model_args.generate_reasoning), \
        "data_args.use_reasoning_as_instructions and model_args.generate_reasoning cannot be set true simultaneously."

    return model_args, data_args, training_args, action_head_args, None

def rank0_print(*args):
    if local_rank == 0:
        print(*args)
def main(all_config=None):
    set_seed(1)
    # command line parameters
    # get task parameters
    task_config = TASK_CONFIGS[all_config['data_args'].task_name]
    dataset_dir = task_config["dataset_dir"]
    episode_len = task_config['episode_len']
    camera_names = task_config['camera_names']
    image_features = [f"observation.images.{cam}" for cam in camera_names]


    all_config['camera_names'] = camera_names
    all_config['episode_len'] = episode_len


    model_init_kwargs = dict(
        vla_model_name=all_config['model_args'].vla_model_name,
        generate_reasoning=all_config['model_args'].generate_reasoning,
        state_dim=all_config['action_head_args'].state_dim,
        action_dim=all_config['action_head_args'].action_dim,
        pretrained_path=all_config['model_args'].pretrain_vlm_backbone_path,
        resize_imgs_with_padding=eval(all_config['data_args'].resize_image_size),
        image_features=image_features,
        attention_implementation=all_config['training_args'].attention_implementation,
        vocab_size=all_config['data_args'].vocab_size,
        action_tokenizer=all_config['data_args'].action_tokenizer,
        state_repr=all_config['data_args'].state_repr,
        mask_strategy=all_config['model_args'].mask_strategy,
        moe=all_config['model_args'].moe,
        routed_expert_num=all_config['model_args'].routed_expert_num,
        routed_top_k=all_config['model_args'].routed_top_k,
        action_loss_weight=all_config['model_args'].action_loss_weight,
        use_adaptive_rms_norm=all_config['model_args'].use_adaptive_rms_norm,
        norm_mode=all_config['data_args'].norm_mode,
    )

    train_dataset, data_stats = load_data(dataset_dir, camera_names, all_config['data_args'].chunk_size,
                                          annotation_dir=all_config['data_args'].annotation_dir,
                                          ckpt_dir=all_config['training_args'].output_dir,
                                          rank0_print=rank0_print,
                                          data_args=all_config['data_args'],
                                          vl_data_json=all_config['data_args'].vl_data_json,
                                          vl_image_dir=all_config['data_args'].vl_image_dir,
                                          use_fast=all_config['data_args'].action_tokenizer != None)

    model = load_vla_model(
        model_init_kwargs=model_init_kwargs,
        device='cuda',
        data_stats=data_stats
    )

    model.requires_grad_(True)
    model.model.action_out_proj = model.model.action_out_proj.to(torch.float32)
    model.model.action_in_proj = model.model.action_in_proj.to(torch.float32)
    if all_config['data_args'].state_repr == 'continuous':
        model.model.state_proj = model.model.state_proj.to(torch.float32)

    for k,v in model.named_parameters():
        if v.requires_grad:
            rank0_print(f'{k}: {v.requires_grad}')

    set_seed(config['training_args'].seed)
    compute_dtype = (torch.float16 if training_args.fp16 else (torch.bfloat16 if config['training_args'].bf16 else torch.float32))

    model.config.use_cache = True
    model.config.save_pretrained(config['training_args'].output_dir)


    trainer = MIRROTrainer(
        model=model,
        args=all_config['training_args'],
        train_dataset=train_dataset,
        data_collator=PIOCollator
    )

    trainer.train(resume_from_checkpoint=all_config['training_args'].resume_from_checkpoint)

    trainer.save_state()

    model.config.use_cache = True


    if all_config['training_args'].local_rank == 0 or all_config['training_args'].local_rank == -1:
        model.config.save_pretrained(all_config['training_args'].output_dir)
        # model.save_pretrained(config['training_args'].output_dir, state_dict=state_dict)

if __name__ == '__main__':
    model_args, data_args, training_args, action_head_args, model_config = parse_param()
    assert data_args.state_repr in ['continuous', 'text'], "data_args.state_repr must be either 'continuous' or 'text'"

    key_para_str = "Key training arguments: "
    # overwrite parameters in pi0_config
    for k in ['stop_gradient', 'generate_reasoning', 'diffusion_strategy', 'mask_strategy', 'use_adaptive_rms_norm']:
        val = getattr(model_args, k)
        key_para_str += f"|{k}: {val} "
    for k in ['action_tokenizer', 'state_repr', 'norm_mode']:
        val = getattr(data_args, k)
        key_para_str += f"|{k}: {val} "

    print(f"{RED}" + key_para_str + f"{RESET}")

    config = {
        'model_args':model_args,
        'data_args':data_args,
        'training_args':training_args,
        'action_head_args':action_head_args,
    }

    config_dict = {k:asdict(v) if not isinstance(v, dict) else v for k,v in config.items()}

    ckpt = os.path.join(config['training_args'].output_dir, f"checkpoint-{config['training_args'].save_steps}")
    if os.path.exists(ckpt):
        config['training_args'].resume_from_checkpoint = True
        rank0_print(f"{RED}Resuming Training............{RESET}")
    main(all_config=config)
    pass


