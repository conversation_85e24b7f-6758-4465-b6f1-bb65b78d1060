# H100 LeRobot Pi0 S2S1 Training

This project is designed to train and evaluate robot manipulation policies, specifically focusing on the LeRobot Pi0 and Deep Diffusion Policy (DeepDP) models. It provides a suite of scripts for data collection, preprocessing, training, and evaluation for robotics tasks.

## 1. Setup

### a. <PERSON>lone the Repository

```bash
git clone <your-repository-url>
cd h100_lerobot_pi0_s2s1
```

### b. Create Conda Environment

A `conda_env.yaml` file is provided to set up the required environment with all necessary dependencies.

```bash
# Create the conda environment from the file
conda env create -f conda_env.yaml

# Activate the environment
conda activate aloha
```

### c. Install Local Packages

The project contains local packages that need to be installed in editable mode. This allows you to make changes to the code without reinstalling.

```bash
# Install the main package
pip install -e .

# Install the policy heads module
pip install -e policy_heads/
```

## 2. Data Preparation

The training pipeline expects data to be in the HDF5 (`.h5`) format.

### a. Data Collection

If you need to collect your own data, you can adapt the scripts provided in `aloha_scripts/`. The `record_episodes.py` script is a good starting point.

### b. Data Preprocessing

Raw data can be processed using the scripts in `data_preprocess_scripts/`. These scripts can be used for tasks like compressing data, adding language annotations, and deleting failed episodes.

### c. Normalization Statistics

Before starting a training run, you must calculate the normalization statistics for your dataset. This is a critical step and is included in the training scripts. The `data_utils/get_norm_stats_by_task.py` script is called automatically by the training shell scripts.

## 3. Training

Training is orchestrated through shell scripts located in the `scripts/` directory. These scripts handle setting up the environment, calculating normalization stats, and launching the training process with `deepspeed`.

**Before running any training script, you must open the file and modify the following variables to match your local paths and configuration:**

*   `mnop`: Path to the pretrained vision-language model (VLM) backbone.
*   `ANNO_DIR`: Path to the directory containing your `.h5` dataset files.
*   `TASKNAME`: A descriptive name for your task.
*   `OUTPUT`: The directory where training checkpoints and logs will be saved.

### a. LeRobot Pi0 Pre-training

To pre-train the LeRobot Pi0 model, use the following script:

```bash
# Modify the variables inside the script first!
bash scripts/aloha/pretrain_lerobot_pi0.sh
```

### b. DeepDP Fine-tuning

To fine-tune the Deep Diffusion Policy model, use the following script:

```bash
# Modify the variables inside the script first!
bash scripts/aloha/finetune_deep_dp_fm.sh
```

Other scripts for fine-tuning LeRobot Pi0 (`finetune_lerobot_pi0.sh`) and training on different hardware setups (e.g., `franka/`) are also available.

## 4. Evaluation

To evaluate a trained policy, use the scripts provided in the `evaluate/` directory. You will need to provide the path to your validation dataset and the checkpoint of the trained model.

Example command for evaluating a LeRobot Pi0 policy:

```bash
python evaluate/eval_mirro.py \
  --dataset_path /path/to/your/validation_data \
  --policy_path /path/to/your/trained_checkpoint \
  --task_name your_task_name
```

Similarly, use `eval_deep_dp.py` for evaluating DeepDP models.

## 5. Codebase Structure

- **`aloha_scripts/`**: Scripts for data collection and robot control (e.g., teleoperation).
- **`data_preprocess_scripts/`**: Utilities for cleaning and preparing HDF5 datasets.
- **`data_utils/`**: Helper functions for dataset loading and statistics calculation.
- **`evaluate/`**: Scripts for running policy evaluations.
- **`lerobot/`**: The core LeRobot library, likely included as a submodule.
- **`pi0/`**: Contains the model definition and trainer for the LeRobot Pi0 policy.
- **`policy_heads/`**: Defines different policy head architectures.
- **`scripts/`**: Main entry point shell scripts for launching training runs.
- **`train_pi0_h5py.py`**: The main Python script for training the Pi0 model.
- **`train_deep_dp_h5py.py`**: The main Python script for training the DeepDP model.

## License

This project is licensed under the terms of the LICENSE file.

