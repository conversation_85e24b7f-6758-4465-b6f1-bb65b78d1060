#!/bin/bash

mnop=/home/<USER>/tzb/wjj/model_param/pi0_lerobot/
vlm_pretrain=/home/<USER>/tzb/wjj/model_param/PaliGemma/paligemma/pixel_224/vla-paligemma-3b-pt-224/

TASKNAME=mobile_franka_ume_bin_picking_bbox

OUTPUT=/home/<USER>/tzb/chenyf/output/pi0_h5_results/${TASKNAME}_finetune

if [ -d "$OUTPUT" ]; then
   echo 'output exists'
else
   echo '!!output not exists!!'
   mkdir -p $OUTPUT
fi

mkdir -p $OUTPUT/src
cp -r ./aloha_scripts $OUTPUT/src/
cp -r ./scripts $OUTPUT/
cp -r ./data_utils $OUTPUT/src/
cp -r ./lerobot $OUTPUT/src/
cp -r ./pi0 $OUTPUT/src/

deepspeed --master_port 29604 --num_gpus=8 --num_nodes=1 ./train_pi0_h5py.py \
  --deepspeed scripts/zero2.json \
  --action_dim 10 \
  --use_reasoning False \
  --load_pretrain True \
  --state_dim 7 \
  --pretrain_vlm_path $vlm_pretrain \
  --home_lerobot "/home/<USER>/tzb/lerobot_data/aloha" \
  --flash_attn False \
  --chunk_size 50 \
  --task_name $TASKNAME \
  --model_name_or_path $mnop \
  --bf16 True \
  --output_dir $OUTPUT \
  --max_steps 60000 \
  --image_size_stable "(320,240)" \
  --image_size_wrist "(320,240)" \
  --per_device_train_batch_size 12 \
  --gradient_accumulation_steps 1 \
  --save_strategy "steps" \
  --save_steps 10000 \
  --save_total_limit 50 \
  --learning_rate 2e-5 \
  --weight_decay 0. \
  --warmup_ratio 0.01 \
  --lr_scheduler_type "cosine" \
  --logging_steps 50 \
  --tf32 True \
  --model_max_length 2048 \
  --gradient_checkpointing True \
  --dataloader_num_workers 8 \
  --concat "token_cat" \
  --report_to tensorboard \
  --logging_dir $OUTPUT/log | tee $OUTPUT/log.log

for dir in "$OUTPUT"/*/ ; do
    # 检查文件夹名称是否包含'checkpoint'
    if [[ "$(basename "$dir")" == *"checkpoint"* ]]; then
        cp ${mnop}/preprocessor_config.json $dir
        cp ${mnop}/chat_template.json $dir
        # cp $OUTPUT/non_lora_trainables.bin $dir
    fi
done

mv ./60030.log $OUTPUT
echo $OUTPUT