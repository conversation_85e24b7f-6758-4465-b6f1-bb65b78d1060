#!/bin/bash
export PYTHONPATH=/home/<USER>/tzb/wjj/projects/mirro
#mnop=/home/<USER>/tzb/wjj/train_results/pi0_lerobot_results/xdof_pretrain_pretrain_xdof_constant_1_epoch_300h/checkpoint-350000
#mnop=/home/<USER>/tzb/wjj/model_param/pi0_lerobot/
#mnop=/home/<USER>/tzb/wjj/model_param/PaliGemma/paligemma/pixel_224/vla-paligemma-3b-pt-224/
mnop=/home/<USER>/tzb/wjj/train_results/mirro_results/xdof_pretrain_0718/checkpoint-120000/
#ANNO_DIR=/home/<USER>/tzb/h5py_data/compressed_data_new/shirts_pants_annotations_seed/pants_standard_compressed_164_0624
ANNO_DIR=/home/<USER>/tzb/h5py_data/compressed_data_new/shirts_pants_annotations_seed/seed_annotation/
#ANNO_DIR="None"
TASKNAME=folding_pants_zby_raise

OUTPUT=/home/<USER>/tzb/wjj/train_results/mirro_results/${TASKNAME}_mirro_s2_vl_with_ac_loss_w10_xdof

if [ -d "$OUTPUT" ]; then
   echo 'output exists'
else
   echo '!!output not exists!!'
   mkdir -p $OUTPUT
fi

mkdir -p $OUTPUT/src
cp -a ./ $OUTPUT/src/
#  --vl_data_json /home/<USER>/tzb/wjj/data/llava_v1_5_mix665k_1_turn_le_100.json \
#  --vl_image_dir /home/<USER>/tzb/zhumj/data/llava_finetune/data \

echo "Calculating norm stats..."
python3 data_utils/get_norm_stats_by_task.py --task_name $TASKNAME --ckpt_dir $OUTPUT

deepspeed --master_port 29604 --num_gpus=8 --num_nodes=1 ./training/co_train_with_vl.py \
  --deepspeed scripts/zero2.json \
  --use_reasoning_as_instructions False \
  --state_repr "text" \
  --vocab_size 257152 \
  --stop_gradient False \
  --use_adaptive_rms_norm False \
  --norm_mode mean_std \
  --action_loss_weight 10 \
  --mask_strategy "reason" \
  --vl_data_json /home/<USER>/tzb/wjj/data/llava_v1_5_mix665k_1_turn_le_100.json \
  --vl_image_dir /home/<USER>/tzb/zhumj/data/llava_finetune/data \
  --flash_attn False \
  --generate_reasoning True \
  --chunk_size 50 \
  --vla_model_name 'mirro_s2' \
  --task_name $TASKNAME \
  --relative_control False \
  --annotation_dir $ANNO_DIR \
  --pretrain_vlm_backbone_path $mnop \
  --bf16 True \
  --output_dir $OUTPUT \
  --max_steps 200000 \
  --resize_image_size "(224,224)" \
  --per_device_train_batch_size 6 \
  --gradient_accumulation_steps 1 \
  --save_strategy "steps" \
  --save_steps 20000 \
  --save_total_limit 50 \
  --learning_rate 2e-5 \
  --weight_decay 0. \
  --warmup_ratio 0.01 \
  --lr_scheduler_type "cosine" \
  --logging_steps 10 \
  --tf32 True \
  --model_max_length 2048 \
  --gradient_checkpointing False \
  --dataloader_num_workers 16 \
  --dataloader_prefetch_factor 2 \
  --dataloader_persistent_workers True \
  --report_to tensorboard \
  --logging_dir $OUTPUT/log | tee $OUTPUT/log.log

mv ./60030.log $OUTPUT
echo $OUTPUT