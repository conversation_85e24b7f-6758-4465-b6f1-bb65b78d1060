#!/bin/bash

mnop=/home/<USER>/tzb/wjj/model_param/PaliGemma/paligemma/pixel_224/vla-paligemma-3b-pt-224/
#ANNO_DIR=/home/<USER>/tzb/h5py_data/compressed_data_new/shirts_pants_annotations_seed/pants_standard_compressed_164_0624

TASKNAME=xdof_pretrain

OUTPUT=/home/<USER>/tzb/wjj/train_results/pi0_lerobot_results/${TASKNAME}_pretrain_xdof_constant_1_epoch_300h


if [ -d "$OUTPUT" ]; then
   echo 'output exists'
else
   echo '!!output not exists!!'
   mkdir -p $OUTPUT
fi

mkdir -p $OUTPUT/src
cp -r ./aloha_scripts $OUTPUT/src/
cp -r ./scripts $OUTPUT/
cp -r ./data_utils $OUTPUT/src/
cp -r ./lerobot $OUTPUT/src/
cp -r ./pi0 $OUTPUT/src/

echo "Calculating norm stats..."
python3 data_utils/get_norm_stats_by_task.py --task_name $TASKNAME --ckpt_dir $OUTPUT

deepspeed --master_port 29604 --num_gpus=8 --num_nodes=1 ./train_pi0_h5py.py \
  --deepspeed scripts/zero2.json \
  --action_dim 20 \
  --use_reasoning_as_instructions False \
  --generate_reasoning False \
  --stop_gradient False \
  --state_dim 20 \
  --flash_attn False \
  --chunk_size 50 \
  --task_name $TASKNAME \
  --annotation_dir $ANNO_DIR \
  --pretrain_vlm_backbone_path $mnop \
  --bf16 True \
  --output_dir $OUTPUT \
  --max_steps 330000 \
  --resize_image_size "(224,224)" \
  --per_device_train_batch_size 12 \
  --gradient_accumulation_steps 1 \
  --save_strategy "steps" \
  --save_steps 30000 \
  --save_total_limit 50 \
  --learning_rate 2e-5 \
  --weight_decay 0. \
  --warmup_ratio 0.01 \
  --lr_scheduler_type "constant" \
  --logging_steps 50 \
  --tf32 True \
  --model_max_length 2048 \
  --gradient_checkpointing True \
  --dataloader_num_workers 8 \
  --report_to tensorboard \
  --logging_dir $OUTPUT/log | tee $OUTPUT/log.log

for dir in "$OUTPUT"/*/ ; do
    # 检查文件夹名称是否包含'checkpoint'
    if [[ "$(basename "$dir")" == *"checkpoint"* ]]; then
        cp ${mnop}/preprocessor_config.json $dir
        cp ${mnop}/chat_template.json $dir
        # cp $OUTPUT/non_lora_trainables.bin $dir
    fi
done

mv ./60030.log $OUTPUT
echo $OUTPUT
