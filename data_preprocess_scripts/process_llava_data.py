import os
import json
from tqdm import tqdm

def generate_single_turn():
    path = "/media/rl/HDD/data/data/llava_v1_5_mix665k.json"

    with open(path, "r") as f:
        data = json.load(f)

    llava_v1_5_mix665k_1_turn = []

    for each in tqdm(data):
        if len(each['conversations']) % 2 != 0:
            print(len(each['conversations']), f"{each['id']} / {each['image']}, drop the last conversation.")
            each['conversations'] = each['conversations'][:-1]
        if 'image' not in each.keys():
            print("Text only data.")
            continue
        for i, conv in enumerate(each['conversations']):
            if i % 2 == 0:
                temp = {
                    'id': each['id'],
                    'image': each['image'],
                    'conversations': [],
                }
            if "<image>\n" not in conv['value'] and conv['from'] == "human":
                value = "<image>\n" + conv['value']
            else:
                value = conv['value']
            temp['conversations'].append({
                'from': conv['from'],
                'value': value,
            })
            if i % 2 == 1:
                llava_v1_5_mix665k_1_turn.append(temp)
                del temp

    print(len(llava_v1_5_mix665k_1_turn))
    with open('/media/rl/HDD/data/data/llava_v1_5_mix665k_1_turn.json', 'w') as f:
        json.dump(llava_v1_5_mix665k_1_turn, f, indent=4)

def filter_conversations():
    path = "/home/<USER>/tzb/wjj/data/llava_v1_5_mix665k_1_turn.json"

    with open(path, "r") as f:
        data = json.load(f)

    new_json = []
    for each in tqdm(data):
        if len(each['conversations'][1]['value'].split(" ")) > 50:
            continue
        else:
            new_json.append(each)

    print(len(new_json))
    with open("/home/<USER>/tzb/wjj/data/llava_v1_5_mix665k_1_turn_filter_long_text.json", 'w') as f:
        json.dump(new_json, f, indent=4)

if __name__ == '__main__':
    filter_conversations()