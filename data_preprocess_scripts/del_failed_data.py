import json
import shutil
import os
import h5py
with open('/home/<USER>/tzb/h5py_data/compressed_data_new/shirts_pants_annotations_seed/failed_compressed_image.json', 'r') as f:
    data = json.load(f)

data_path = "/home/<USER>/tzb/h5py_data/compressed_data_new/mobile_aloha_4_wheels"

for k,v in data.items():
    k = k.replace("20250527_fold_pants_lyp_compressed", "folding_pants_lyp_20250527_compressed")
    task, episode = k.split("/")[-2:]
    if os.path.exists(os.path.join(data_path, task, episode)):
        os.remove(f'{data_path}/{task}/{episode}')
        print(f'mobile_aloha_4_wheels/{task}/{episode} deleted')
    else:
        print(f'{data_path}/{task}/{episode} not found')
