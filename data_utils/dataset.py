import numpy as np
import torch
import os
import h5py
import pickle
import fnmatch
import cv2
from time import time
from torch.utils.data import TensorDataset, DataLoader
import torchvision.transforms as transforms
from torchvision.transforms.functional import to_pil_image, to_tensor
import copy

from transformers import AutoTokenizer, AutoModel

from data_utils.constants import RESET, RED, YELLOW
import tqdm, json

xdof_instruction={
    "fold_shorts":"Fold shorts.",
    "fold_towel":"Fold towel.",
    "folding_skirt":"Fold skirt.",
    "folding_tank_top":"Fold tank top.",
    "folding_trousers":"Fold trousers.",
    "folding_tshirt":"Fold t-shirt.",
    "place_trousers_on_hanger":"Place trousers on hanger.",
    "place_tshirt_on_hanger":"Place t-shirt on hanger.",
    "placing_glasses_into_tray":"Place glasses into tray.",
    "set_dinner_table_for_1_person_plate_utensils_glass":"Set dinner table for one person.",
    "load_plastic_bag":"Load plastic bag.",
    "place_fake_fruits_into_plastic_bag":"Place fruits into plastic bag.",
    "sorting_screws_and_nuts_into_containers":"Sort screws and nuts into containers."
}
pretrain_instruction={
    "fold_shorts":"Fold shorts.",
    "fold_towel":"Fold towel.",
    "folding_skirt":"Fold skirt.",
    "folding_tank_top":"Fold tank top.",
    "folding_trousers":"Fold trousers.",
    "folding_tshirt":"Fold t-shirt.",
    "place_trousers_on_hanger":"Place trousers on hanger.",
    "place_tshirt_on_hanger":"Place t-shirt on hanger.",
    "placing_glasses_into_tray":"Place glasses into tray.",
    "set_dinner_table_for_1_person_plate_utensils_glass":"Set dinner table for one person.",
    "load_plastic_bag":"Load plastic bag.",
    "place_fake_fruits_into_plastic_bag":"Place fruits into plastic bag.",
    "sorting_screws_and_nuts_into_containers":"Sort screws and nuts into containers.",
    "folding_pants": "Fold clothes.",
    "folding_shirts": "Fold clothes.",
    "plastic_bag": "Open the plastic bag and fetch all items in it.",
    "sofa": "collect clothes from sofa and drop into basket.",
    # "washing_machine": "collect clothes from washing machine and drop into basket.",
    # "sofa_move": "put items into the basket and move in front of fridge"


    "folding_clothes": "Fold clothes",
    "collect_clothes": "Collect clothes from washing machine and drop into basket.",
    "store_items": "Place items into fridge.",
    "move_fridge2": "Place items into fridge.",
    "move_fridge_": "Put items into basket and approach fridge."
}

def flatten_list(l):
    return [item for sublist in l for item in sublist]
import gc
class EpisodicDataset(torch.utils.data.Dataset):
    def __init__(self, dataset_path_list, camera_names, norm_stats, episode_ids, episode_len, chunk_size, languages_reasonings=None, robot=None, rank0_print=print, llava_pythia_process=None, data_args=None):
        super(EpisodicDataset).__init__()
        self.episode_ids = episode_ids
        self.dataset_path_list = dataset_path_list
        self.camera_names = camera_names
        self.norm_stats = norm_stats
        self.episode_len = episode_len
        self.chunk_size = chunk_size
        self.cumulative_len = np.cumsum(self.episode_len)
        self.max_episode_len = max(episode_len)
        self.llava_pythia_process = llava_pythia_process
        self.data_args = data_args
        self.robot = robot
        self.rank0_print = rank0_print
        self.languages_reasonings = languages_reasonings

        self.transformations = None
        # _ = self.__getitem__(0) # initialize self.is_sim and self.transformations
        # ctr_mode = 'relative' if self.data_args.relative_control else 'absolute'
        ctr_mode = 'absolute'
        self.rank0_print(f"The robot is {RED} {self.robot} {RESET} | The camera views: {RED} {self.camera_names} {RESET} | Control Mode: {ctr_mode} {RESET}")
        self.is_sim = False

    def __len__(self):
        return sum(self.episode_len)

    def _locate_transition(self, index):
        assert index < self.cumulative_len[-1]
        episode_index = np.argmax(self.cumulative_len > index) # argmax returns first True index
        start_ts = index - (self.cumulative_len[episode_index] - self.episode_len[episode_index])
        episode_id = self.episode_ids[episode_index]
        return episode_id, start_ts

    def load_from_h5(self, dataset_path, start_ts):
        with h5py.File(dataset_path, 'r') as root:
            try: # some legacy data does not have this attribute
                is_sim = root.attrs['sim']
            except:
                is_sim = False
            compressed = root.attrs.get('compress', False)
            if 'xdof' in dataset_path:
                compressed = True
            task, episode_id = dataset_path.split('/')[-2:]
            episode_id = episode_id.split(".")[0]
            # TODO(ZWW): temp comment
            # if 'xdof' in dataset_path:
            #     raw_lang = xdof_instruction[task]
            #     reasoning = raw_lang
            if self.languages_reasonings is not None:
                raw_lang = self.languages_reasonings[task].get(episode_id, {}).get('instructions', None)
                if not raw_lang:
                    for task_key,task_value in pretrain_instruction.items():
                        if task_key in task:
                            raw_lang=task_value
                            break

                try:
                    r_idx = np.argmax(np.array(self.languages_reasonings[task][episode_id]['index_list']) > start_ts)
                    reasoning = self.languages_reasonings[task].get(episode_id, {}).get('reasoning_list', [])[r_idx]
                except Exception as e:
                    self.rank0_print(e)
                    reasoning = "None"
                if not reasoning:
                    reasoning="None"
                if not raw_lang:
                    raw_lang="None"
            else:
                raw_lang ="None"
                reasoning = "None"
            try:  # only used for agelix and franka
                qpos = root['/observations/qpos'][()]
                action = root['/action'][()][:]
            except:  # for mobile aloha
                qpos = np.concatenate([
                    root['/state/joint_position/left'][()][:-1],
                    root['/state/joint_position/right'][()][:-1],
                    # root['/state/base_vel'][()][:-1]
                ], axis=1)
                action = np.concatenate([
                    root['/state/joint_position/left'][()][1:],
                    root['/state/joint_position/right'][()][1:],
                    # root['/action/base_vel'][()][:-1]
                ], axis=1)

            original_action_shape = action.shape
            episode_len = original_action_shape[0]

            # get observation at start_ts only
            resize_size = None
            all_cam_images = []
            for cam_name in self.camera_names:
                image = root[f'/observations/images/{cam_name}'][start_ts]
                try:
                    if compressed:
                        image = cv2.imdecode(image, 1)
                    if image.shape[0] != 3:
                        image = np.transpose(image, (2, 0, 1))
                    image = np.array(image)
                except Exception:
                    self.rank0_print("image shape none,add black img")
                    image = np.zeros((3, 480, 640))  # TODO(ZWW)
                image = torch.from_numpy(image)
                if resize_size is None:
                    resize_size = (480, 640)
                # if self.transformations is None:
                #     self.rank0_print('Initializing transformations')
                original_size = image.shape[-2:]
                ratio = 0.95
                self.transformations = [
                    transforms.RandomCrop(size=[int(original_size[0] * ratio), int(original_size[1] * ratio)]),
                    transforms.Resize(resize_size, antialias=True),
                    transforms.RandomRotation(degrees=[-5.0, 5.0], expand=False),
                    transforms.ColorJitter(brightness=0.3, contrast=0.4, saturation=0.5)  # , hue=0.08)
                ]

                for transform in self.transformations:
                    image = transform(image)
                image = image / 255.0
                all_cam_images.append(image)

            qpos = qpos[start_ts]
            if is_sim:
                action = action[start_ts:]
                action_len = episode_len - start_ts
            else:
                action = action[max(0, start_ts - 1):] # hack, to make timesteps more aligned
                action_len = episode_len - max(0, start_ts - 1) # hack, to make timesteps more aligned
        return original_action_shape, action, action_len, all_cam_images, qpos, raw_lang, reasoning
    def __getitem__(self, index):
        episode_id, start_ts = self._locate_transition(index)
        dataset_path = self.dataset_path_list[episode_id]
        try:
            original_action_shape, action, action_len, all_cam_images, qpos, raw_lang, reasoning = self.load_from_h5(dataset_path, start_ts)
        except Exception as e:
            print(f"Read {dataset_path} happens {YELLOW}{e}{RESET}")
            try:
                dataset_path = self.dataset_path_list[episode_id + 1]
            except Exception as e:
                dataset_path = self.dataset_path_list[episode_id - 1]

            original_action_shape, action, action_len, all_cam_images, qpos, raw_lang, reasoning = self.load_from_h5(dataset_path, start_ts)

        # self.is_sim = is_sim
        padded_action = np.zeros((self.max_episode_len, original_action_shape[1]), dtype=np.float32)

        padded_action[:action_len] = action
        is_pad = np.zeros(self.max_episode_len)
        is_pad[action_len:] = 1

        padded_action = padded_action[:self.chunk_size]

        #ToDo relative control needs to modify the norm_stats calculation
        # if self.data_args.relative_control:
        #     padded_action = padded_action - qpos

        is_pad = is_pad[:self.chunk_size]

        # new axis for different cameras

        try:
            image_data = np.stack(all_cam_images, axis=0)
        except Exception as e:
            for each in all_cam_images:
                print(each.shape)
            exit(0)

        # construct observations
        qpos_data = torch.from_numpy(qpos).float()
        action_data = torch.from_numpy(padded_action).float()
        is_pad = torch.from_numpy(is_pad).bool()

        # image_data = torch.einsum('k h w c -> k c h w', image_data)

        sample = {}
        for i,cam_name in enumerate(self.camera_names):
            sample[f"observation.images.{cam_name}"] = image_data[i]

        if self.data_args.use_reasoning_as_instructions:
            sample['task'] = reasoning
        else:
            sample['task'] = raw_lang
        sample['observation.state'] = qpos_data
        sample['action'] = action_data
        sample['action_is_pad'] = is_pad
        sample['reasoning'] = reasoning
        interval = np.random.randint(150, 200)
        sample["is_s1"] = torch.Tensor([start_ts % interval != 0]).to(torch.bool)
        sample["is_vl_data"] = False
        assert raw_lang is not None, ""
        if index == 0:
            self.rank0_print(reasoning)
        del image_data
        del qpos_data
        del action_data
        del is_pad
        del raw_lang
        del reasoning
        gc.collect()

        return sample

def find_all_hdf5(dataset_dir, skip_mirrored_data, rank0_print=print):
    hdf5_files = []
    for root, dirs, files in os.walk(dataset_dir):
        if 'pointcloud' in root: continue
        for filename in fnmatch.filter(files, '*.hdf5'):
            if 'features' in filename: continue
            if skip_mirrored_data and 'mirror' in filename:
                continue
            hdf5_files.append(os.path.join(root, filename))
    if len(hdf5_files) == 0:
        rank0_print(f"{RED} Found 0 hdf5 datasets found in {dataset_dir} {RESET}")
        exit(0)
    rank0_print(f'Found {len(hdf5_files)} hdf5 files')
    return hdf5_files


def load_data(dataset_dir_l, camera_names, chunk_size, annotation_dir="", ckpt_dir="", rank0_print=print, data_args=None, distillbert_dir=None):
    with open(os.path.join(ckpt_dir, "dataset_stats.pkl"), "rb") as f:
        norm_stats = pickle.load(f)

    dataset_path_list = norm_stats["dataset_path_list"]
    train_episode_ids = np.arange(len(dataset_path_list))
    train_episode_len = norm_stats["all_episode_len"]

    if annotation_dir != None and annotation_dir != "None":
        languages_reasonings = {}
        for each in tqdm.tqdm(dataset_dir_l, desc="Merging languages json files....."):
            dir_name, task = each.split('/')[-2:]

            with open(os.path.join(annotation_dir, f"{dir_name}/{task}/{task}.json"), 'r') as f:
                anno_data_json = json.load(f)
            temp = {}
            for k,v in anno_data_json.items():
                index_list = [e['end_frame'] for e in v['actions']]
                reasoning_list = [e['description'] for e in v['actions']]
                temp[k] = dict(index_list=index_list, reasoning_list=reasoning_list, instructions=v['title'])
            languages_reasonings[task] = temp

        with open(os.path.join(ckpt_dir, "languages_reasonings.json"), "w") as f:
            json.dump(languages_reasonings, f, indent=4)

        rank0_print(
            f">>>>>>>>>>>>>>>>>>>>>>>>>>Checking all episodes languages and reasonings...>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")
        for each in dataset_path_list:
            task, episode = each.split('/')[-2:]
            episode = episode.split('.')[0]
            _ = languages_reasonings[task][episode]
    else:
        languages_reasonings = None

    for d, dpl in zip(dataset_dir_l, dataset_path_list):
        if len(dpl) == 0:
            rank0_print("#2" * 20)
            rank0_print(d)

    print(f'{RED} Data from: {dataset_dir_l}\n- Train on total of {len(dataset_path_list)} episodes | {sum(train_episode_len)} steps. {RESET}')


    train_dataset = EpisodicDataset(dataset_path_list=dataset_path_list, camera_names=camera_names, norm_stats=norm_stats, episode_ids=train_episode_ids,
                                    episode_len=train_episode_len, chunk_size=chunk_size, languages_reasonings=languages_reasonings, data_args=data_args)

    train_dataset.__getitem__(0)
    return train_dataset, norm_stats


def calibrate_linear_vel(base_action, c=None):
    if c is None:
        c = 0.0 # 0.19
    v = base_action[..., 0]
    w = base_action[..., 1]
    base_action = base_action.copy()
    base_action[..., 0] = v - c * w
    return base_action

def smooth_base_action(base_action):
    return np.stack([
        np.convolve(base_action[:, i], np.ones(5)/5, mode='same') for i in range(base_action.shape[1])
    ], axis=-1).astype(np.float32)

def postprocess_base_action(base_action):
    linear_vel, angular_vel = base_action
    linear_vel *= 1.0
    angular_vel *= 1.0

    return np.array([linear_vel, angular_vel])

### env utils

def compute_dict_mean(epoch_dicts):
    result = {k: None for k in epoch_dicts[0]}
    num_items = len(epoch_dicts)
    for k in result:
        value_sum = 0
        for epoch_dict in epoch_dicts:
            value_sum += epoch_dict[k]
        result[k] = value_sum / num_items
    return result

def detach_dict(d):
    new_d = dict()
    for k, v in d.items():
        new_d[k] = v.detach()
    return new_d

def set_seed(seed):
    torch.manual_seed(seed)
    np.random.seed(seed)
