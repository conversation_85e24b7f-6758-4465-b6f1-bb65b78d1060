import numpy as np
import torch
import torchvision.io as vio
from torchvision.io import ImageReadMode
from data_utils.dataset import *
import random
from PIL import Image
def flatten_list(l):
    return [item for sublist in l for item in sublist]
import gc

def encode_state_to_str(states, device='cpu'):
    # assert torch.max(states) <= 1 and torch.min(states) >= -1, f"States must be normalized between -1 and 1"

    bins = torch.linspace(-1, 1, 256 + 1, device=device)[:-1]
    discretized = torch.bucketize(states, bins) - 1
    state_str = " ".join(str(val.item()) for val in discretized)

    return state_str
class EpisodicDatasetWithVL(EpisodicDataset):
    def __init__(self, dataset_path_list, camera_names, norm_stats, episode_ids, episode_len, chunk_size, use_fast=False, languages_reasonings=None, robot=None, rank0_print=print, llava_pythia_process=None, data_args=None, vl_data_json=None, vl_image_dir=None):
        super().__init__(dataset_path_list, camera_names, norm_stats, episode_ids, episode_len, chunk_size, languages_reasonings, robot, rank0_print, llava_pythia_process, data_args)
        self.state_repr = data_args.state_repr
        self.use_fast = use_fast
        if vl_data_json is not None:
            random.seed(42)
            vl_data_list = json.load(open(vl_data_json, 'r'))

            max_robot_data_len = self.cumulative_len[-1]

            print(f"########################Current {RED} VL DATA : Robot DATA = {len(vl_data_list)} : {max_robot_data_len} || {len(vl_data_list) / max_robot_data_len:.1f} : 1 {RESET}###################################")
            vl_data_list = vl_data_list[:int(max_robot_data_len / 10)]
            self.episode_len.append(len(vl_data_list))
            self.cumulative_len = np.append(self.cumulative_len, max_robot_data_len + len(vl_data_list))
            max_epi = max(self.episode_ids)
            self.episode_ids = np.append(self.episode_ids, max_epi + 1)

        else:
            vl_data_list = None
        self.vl_data_list = vl_data_list
        self.vl_image_dir = vl_image_dir
    def __getitem__(self, index):
        episode_id, start_ts = self._locate_transition(index)
        is_vl_data = self.vl_data_list is not None and self.episode_ids.shape[0] != 1 and episode_id == self.episode_ids[-1]
        if is_vl_data:  # vl data only
            source = self.vl_data_list[start_ts]
            raw_lang = source['conversations'][0]['value'].replace('<image>', '').replace('\n', '')
            reasoning = source['conversations'][1]['value']
            # image_data = np.zeros((240, 320, 3))
            img_path = source["image"]

            img_array = vio.read_image(os.path.join(self.vl_image_dir, img_path), vio.ImageReadMode.RGB) / 255.0
            # img_array = (torch.from_numpy(np.array(Image.open(os.path.join("/home/<USER>/Pictures", "cat.jpeg")).convert("RGB"))) / 255.0).permute((2, 0, 1))
            resize_transform = transforms.Resize((480, 640), antialias=True)
            img_array = resize_transform(img_array)
            qpos_data = torch.ones((14,)).float()
            action_data = torch.zeros((self.chunk_size, 14)).float()
            is_pad = torch.ones((self.chunk_size,)).bool()
            sample = {}
            for i,cam_name in enumerate(self.camera_names):
                if i == 0:
                    sample[f"observation.images.{cam_name}"] = img_array.cpu().numpy()
                else:
                    sample[f"observation.images.{cam_name}"] = torch.zeros_like(img_array).cpu().numpy()
            sample['task'] = raw_lang
            sample["is_s1"] = torch.Tensor([False]).to(torch.bool)
        else:
            dataset_path = self.dataset_path_list[episode_id]
            try:
                original_action_shape, action, action_len, all_cam_images, qpos, raw_lang, reasoning = self.load_from_h5(dataset_path, start_ts)
            except Exception as e:
                print(f"Read {dataset_path} happens {YELLOW}{e}{RESET}")
                try:
                    dataset_path = self.dataset_path_list[episode_id + 1]
                except Exception as e:
                    dataset_path = self.dataset_path_list[episode_id - 1]

                original_action_shape, action, action_len, all_cam_images, qpos, raw_lang, reasoning = self.load_from_h5(dataset_path, start_ts)

            # self.is_sim = is_sim
            padded_action = np.zeros((self.max_episode_len, original_action_shape[1]), dtype=np.float32)

            padded_action[:action_len] = action
            is_pad = np.zeros(self.max_episode_len)
            is_pad[action_len:] = 1

            padded_action = padded_action[:self.chunk_size]

            #ToDo relative control needs to modify the norm_stats calculation
            # if self.data_args.relative_control:
            #     padded_action = padded_action - qpos

            is_pad = is_pad[:self.chunk_size]

            # new axis for different cameras

            try:
                image_data = np.stack(all_cam_images, axis=0)
            except Exception as e:
                for each in all_cam_images:
                    print(each.shape)
                exit(0)

            # construct observations
            qpos_data = torch.from_numpy(qpos).float()
            action_data = torch.from_numpy(padded_action).float()
            is_pad = torch.from_numpy(is_pad).bool()

            if self.state_repr == 'text':
                state_str = encode_state_to_str(qpos_data)
                raw_lang = f"Task: {raw_lang} State: {state_str};"
            if self.use_fast:
                reasoning += "Action: "
            # image_data = torch.einsum('k h w c -> k c h w', image_data)

            sample = {}
            for i,cam_name in enumerate(self.camera_names):
                sample[f"observation.images.{cam_name}"] = image_data[i]

            if self.data_args.use_reasoning_as_instructions:
                sample['task'] = reasoning
            else:
                sample['task'] = raw_lang
            del image_data
            interval = np.random.randint(150, 200)
            sample["is_s1"] = torch.Tensor([start_ts % interval != 0]).to(torch.bool)

        sample['observation.state'] = qpos_data
        sample['action'] = action_data
        sample['action_is_pad'] = is_pad
        sample['reasoning'] = reasoning

        sample["is_vl_data"] = is_vl_data
        assert raw_lang is not None, ""
        del qpos_data
        del action_data
        del is_pad
        del raw_lang
        del reasoning

        return sample

def load_data(dataset_dir_l, camera_names, chunk_size, annotation_dir="None", ckpt_dir="", use_fast=False, rank0_print=print, data_args=None, vl_data_json=None, vl_image_dir=None):
    with open(os.path.join(ckpt_dir, "dataset_stats.pkl"), "rb") as f:
        norm_stats = pickle.load(f)

    dataset_path_list = norm_stats["dataset_path_list"]
    train_episode_ids = np.arange(len(dataset_path_list))
    train_episode_len = norm_stats["all_episode_len"]

    if annotation_dir != None and annotation_dir != "None":
        languages_reasonings = {}
        for each in tqdm.tqdm(dataset_dir_l, desc="Merging languages json files....."):
            dir_name, task = each.split('/')[-2:]
            try:#TODO(ZWW)
                annotation_path=os.path.join(annotation_dir, f"{dir_name}/{task}/{task}.json")
                annotation_path_backup=annotation_path.replace("seed_manual_annotation","seed_annotation")
                if not os.path.exists(annotation_path):
                    annotation_path=annotation_path_backup
                    rank0_print(f"annotation path has changed: {annotation_path}")
                with open(annotation_path, 'r') as f:
                    anno_data_json = json.load(f)
                temp = {}
                for k,v in anno_data_json.items():
                    index_list = [e['end_frame'] for e in v['actions']]
                    reasoning_list = [e['description'] for e in v['actions']]
                    temp[k] = dict(index_list=index_list, reasoning_list=reasoning_list, instructions=v['title'])
                languages_reasonings[task] = temp
            except Exception as e:
                rank0_print(e)
                rank0_print("---------language reason set none-------")
                languages_reasonings[task]={}

        with open(os.path.join(ckpt_dir, "languages_reasonings.json"), "w") as f:
            json.dump(languages_reasonings, f, indent=4)

        rank0_print(
            f">>>>>>>>>>>>>>>>>>>>>>>>>>Checking all episodes languages and reasonings...>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")
        for each in dataset_path_list:
            task, episode = each.split('/')[-2:]
            episode = episode.split('.')[0]
            try:
                _ = languages_reasonings[task][episode]
            except Exception:
                rank0_print("failed language reason:", each)
    else:
        languages_reasonings = None

    for d, dpl in zip(dataset_dir_l, dataset_path_list):
        if len(dpl) == 0:
            rank0_print("#2" * 20)
            rank0_print(d)

    print(f'{RED} Data from: {dataset_dir_l}\n- Train on total of {len(dataset_path_list)} episodes | {sum(train_episode_len)} steps. {RESET}')


    train_dataset = EpisodicDatasetWithVL(dataset_path_list=dataset_path_list,
                                    camera_names=camera_names,
                                    norm_stats=norm_stats,
                                    episode_ids=train_episode_ids,
                                    episode_len=train_episode_len,
                                    chunk_size=chunk_size,
                                    languages_reasonings=languages_reasonings,
                                    data_args=data_args,
                                    vl_data_json=vl_data_json,
                                    vl_image_dir=vl_image_dir,
                                    use_fast=use_fast)

    example = train_dataset.__getitem__(len(train_dataset) - 100)
    rank0_print(f"{RED}Example VL data: {RESET}\n" + f">>|Task|: {example['task']}<< \n >>|Reasoning|: {example['reasoning']}<<")
    example = train_dataset.__getitem__(100)
    rank0_print(f"{RED}Example Robot data: {RESET}\n" + f">>|Task|: {example['task']}<< \n >>|Reasoning|: {example['reasoning']}<<")
    return train_dataset, norm_stats

if __name__ == "__main__":
    from data_utils.constants import TASK_CONFIGS
    from torch.utils.data import DataLoader, RandomSampler
    from data_utils.data_collator import PIOCollator
    dataset_dir_l = TASK_CONFIGS['folding_pants_zby_raise']["dataset_dir"]
    camera_names = TASK_CONFIGS['folding_pants_zby_raise']['camera_names']
    vl_data_json = "/home/<USER>/tzb/wjj/data/llava_v1_5_mix665k_1_turn_filter_long_text.json"
    vl_image_dir = "/home/<USER>/tzb/zhumj/data/llava_finetune/data"

    from dataclasses import dataclass
    @dataclass
    class aa:
        use_reasoning_as_instructions: bool = False
        state_repr: str = 'continuous'

    train_dataset, norm_stats = load_data(
        dataset_dir_l, camera_names,
        chunk_size=50, data_args=aa,
        vl_data_json=vl_data_json,
        vl_image_dir=vl_image_dir,
        ckpt_dir="/home/<USER>/tzb/wjj/train_results/mirro_results/folding_pants_zby_raise_mirro_s2_co_train_vl_no_fast_mask_generated_tokens_sg2/"
    )

    dl = DataLoader(train_dataset, batch_size=6, shuffle=True, collate_fn=PIOCollator, num_workers=8)
    t1 = t0 = tm.perf_counter()
    for batch in dl:
        t1 = tm.perf_counter()
        # for k, v in batch.items():
        #     if isinstance(v, torch.Tensor):
        #         print(f"{k}: {v.shape}")
        #     else:
        #         print(f"{k}: {v[0]}")
        print(batch['is_vl_data'], torch.sum(batch['is_vl_data']))
        t3 = tm.perf_counter()
        print(f"{t1 - t0 - (t3 - t1):.2f}")
        t0 = t1