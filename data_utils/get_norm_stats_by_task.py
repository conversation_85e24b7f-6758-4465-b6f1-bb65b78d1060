import os
import argparse
import pickle
import h5py
import fnmatch
import torch
import numpy as np

import sys
from data_utils.constants import TASK_CONFIGS

def flatten_list(l):
    return [item for sublist in l for item in sublist]

def get_norm_stats(dataset_path_list):
    all_qpos_data = []
    all_action_data = []
    all_episode_len = []

    for dataset_path in dataset_path_list:
        # try:
        with h5py.File(dataset_path, 'r') as root:
            try:  # only used for agelix and franka
                qpos = root['/observations/qpos'][()]
                action = root['/action'][()][:]
            except:  # for mobile aloha
                qpos = np.concatenate([
                    root['/state/joint_position/left'][()][:-1],
                    root['/state/joint_position/right'][()][:-1],
                    # root['/state/base_vel'][()][:-1]
                ], axis=1)
                action = np.concatenate([
                    root['/state/joint_position/left'][()][1:],
                    root['/state/joint_position/right'][()][1:],
                    # root['/action/base_vel'][()][:-1]
                ], axis=1)
        # except Exception as e:
        #     print(f'Error loading {dataset_path} in get_norm_stats')
        #     print(e)

        all_qpos_data.append(torch.from_numpy(qpos))
        all_action_data.append(torch.from_numpy(action))
        all_episode_len.append(len(qpos))

    all_qpos_data = torch.cat(all_qpos_data, dim=0)
    all_action_data = torch.cat(all_action_data, dim=0)

    # normalize action data
    action_mean = all_action_data.mean(dim=[0]).float()
    action_std = all_action_data.std(dim=[0]).float()
    action_std = torch.clip(action_std, 1e-2, np.inf)  # clipping

    # normalize qpos data
    qpos_mean = all_qpos_data.mean(dim=[0]).float()
    qpos_std = all_qpos_data.std(dim=[0]).float()
    qpos_std = torch.clip(qpos_std, 1e-2, np.inf)  # clipping

    action_min = all_action_data.min(dim=0).values.float()
    action_max = all_action_data.max(dim=0).values.float()
    action_q01 = np.percentile(all_action_data.numpy(), 1, axis=0)
    action_q99 = np.percentile(all_action_data.numpy(), 99, axis=0)

    qpos_min = all_qpos_data.min(dim=0).values.float()
    qpos_max = all_qpos_data.max(dim=0).values.float()
    qpos_q01 = np.percentile(all_qpos_data.numpy(), 1, axis=0)
    qpos_q99 = np.percentile(all_qpos_data.numpy(), 99, axis=0)

    del all_action_data, all_qpos_data

    eps = 0.0001
    stats = {"action_mean": action_mean.numpy(), "action_std": action_std.numpy(),
             "action_min": action_min.numpy() - eps, "action_max": action_max.numpy() + eps,
             "qpos_min": qpos_min.numpy() - eps, "qpos_max": qpos_max.numpy() + eps,
             "qpos_mean": qpos_mean.numpy(), "qpos_std": qpos_std.numpy(),
             "example_qpos": qpos, "all_episode_len": all_episode_len, "dataset_path_list": dataset_path_list,
             "qpos_q01": qpos_q01, "qpos_q99": qpos_q99, "action_q01": action_q01, "action_q99": action_q99}

    return stats

def find_all_hdf5(dataset_dir):
    hdf5_files = []
    for root, dirs, files in os.walk(dataset_dir):
        for filename in fnmatch.filter(files, '*.hdf5'):
            if 'features' in filename:
                continue
            if 'pointclouds' in filename:
                continue
            hdf5_files.append(os.path.join(root, filename))
    print(f'Found {len(hdf5_files)} hdf5 files')
    return hdf5_files

def save_stats(args):
    task_config = TASK_CONFIGS[args.task_name]
    dataset_dir_l = task_config['dataset_dir']
    dataset_path_list_list = [find_all_hdf5(dataset_dir) for dataset_dir in dataset_dir_l]
    dataset_path_list = flatten_list(dataset_path_list_list)
    stats = get_norm_stats(dataset_path_list)
    stats_path = os.path.join(args.ckpt_dir, f'dataset_stats.pkl')
    with open(stats_path, 'wb') as f:
        pickle.dump(stats, f)

if __name__ == '__main__':
    parser = argparse.ArgumentParser()

    ### training hyperparameters
    parser.add_argument('--ckpt_dir', action='store', type=str, help='ckpt dir used for saving traing ckpts', required=True)
    parser.add_argument('--task_name', action='store', type=str, help='task_name', required=True)

    args = parser.parse_args()
    save_stats(args)