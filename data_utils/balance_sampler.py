import math
import numpy as np
import torch
import torch.distributed as dist


class BalancedTwoTypeBatchSampler:
    """
    每个 batch 按 ratio=(s1_ratio, s2_ratio) 同时抽取 s1 / s2。
    * 一个 epoch 内 100% 用尽 s1；s2 不足自动过采样（不复制大数组）。
    * BatchSampler 语义：__iter__ 每次 yield python list[int]。
    """

    def __init__(
        self,
        dataset,
        batch_size: int = 4,
        ratio=(3, 1),
        shuffle: bool = True,
        drop_last: bool = True,
        seed: int = 42,
        interval_range=(150, 200),
    ):
        if not (dist.is_available() and dist.is_initialized()):
            raise RuntimeError("请先调用 torch.distributed.init_process_group()")

        self.ds = dataset
        self.batch_size = batch_size
        self.r1, self.r2 = ratio
        self.shuffle = shuffle
        self.drop_last = drop_last
        self.seed = seed
        self.interval = 200  # 固定使用200，保持与其他采样器一致

        # ---------- 1. batch 内配额 ----------
        total = self.r1 + self.r2
        self.p1 = max(1, self.batch_size * self.r1 // total)
        self.p2 = self.batch_size - self.p1

        # ---------- 2. 预分类 ----------
        s1_idx, s2_idx = [], []
        for i in range(len(dataset)):
            _, ts = dataset._locate_transition(i)
            (s2_idx if ts % self.interval == 0 else s1_idx).append(i)
        if not s1_idx or not s2_idx:
            raise ValueError("两类样本任意一类数量为 0，无法按比例采样。")

        # numpy 化
        self.s1 = np.asarray(s1_idx, dtype=np.int64)
        self.s2 = np.asarray(s2_idx, dtype=np.int64)

        # ---------- 3. 计算批次数 (基于s1数据量) ----------
        self.world = dist.get_world_size()
        self.rank = dist.get_rank()
        self.nb = len(self.s1) // (self.p1 * self.world)
        if not drop_last and len(self.s1) % (self.p1 * self.world):
            self.nb += 1

        # ---------- 4. s2过采样预计算 ----------
        self.need_s2_total = self.nb * self.p2 * self.world
        self.s2_len = len(self.s2)
        
        # 计算需要重复s2多少次
        if self.need_s2_total > self.s2_len:
            self.s2_repeat_times = math.ceil(self.need_s2_total / self.s2_len)
        else:
            self.s2_repeat_times = 1
            
        # 验证采样器配置
        print(f"BALANCED_SAMPLER RANK {self.rank}: s1={len(self.s1)}, s2={len(self.s2)}, "
              f"batch_size={self.batch_size}, ratio={self.r1}:{self.r2}, "
              f"p1={self.p1}, p2={self.p2}, nb={self.nb}, s2_repeat={self.s2_repeat_times}")

    # ------------------------------------------------------------------
    def __iter__(self):
        epoch = getattr(self, "epoch", 0)
        # 使用全局种子确保所有rank生成相同的随机序列
        global_rng = np.random.default_rng(self.seed + epoch)

        # 生成全局洗牌序列（所有rank保持一致）
        if self.shuffle:
            order_s1 = global_rng.permutation(len(self.s1))
            order_s2 = global_rng.permutation(len(self.s2))
        else:
            order_s1 = np.arange(len(self.s1))
            order_s2 = np.arange(len(self.s2))

        # 获取洗牌后的数据
        s1_shuffled = self.s1[order_s1]
        s2_shuffled = self.s2[order_s2]

        # 创建过采样的s2数据
        if self.s2_repeat_times > 1:
            # 重复s2数据以满足需求
            s2_extended = np.tile(s2_shuffled, self.s2_repeat_times)
        else:
            s2_extended = s2_shuffled

        # 为当前rank分配数据（交错分配确保均匀性）
        s1_rank = s1_shuffled[self.rank::self.world]
        s2_rank = s2_extended[self.rank::self.world]

        # 确保s2_rank有足够的数据
        needed_s2_rank = self.nb * self.p2
        if len(s2_rank) < needed_s2_rank:
            # 如果还是不够，继续循环使用
            repeat_factor = math.ceil(needed_s2_rank / len(s2_rank))
            s2_rank = np.tile(s2_rank, repeat_factor)

        ptr1 = ptr2 = 0
        for _ in range(self.nb):
            # 获取s1和s2的部分数据
            part1 = s1_rank[ptr1:ptr1 + self.p1]
            part2 = s2_rank[ptr2:ptr2 + self.p2]
            ptr1 += self.p1
            ptr2 += self.p2

            # 组合成batch（s1在前，s2在后）
            batch_np = np.concatenate((part1, part2))
            
            # 如果需要，可以在batch内部再次洗牌
            if self.shuffle:
                local_rng = np.random.default_rng(self.seed + epoch + ptr1 + ptr2)
                local_rng.shuffle(batch_np)
            
            # 验证batch内容（调试用）
            if (_ < 3):  # 只打印前3个batch
                s1_count = s2_count = 0
                for idx in batch_np:
                    _, ts = self.ds._locate_transition(idx)
                    if ts % self.interval == 0:
                        s2_count += 1
                    else:
                        s1_count += 1
                print(f"BALANCED_BATCH RANK {self.rank} batch {_}: "
                      f"s1={s1_count}, s2={s2_count}, indices={batch_np[:4]}...")
                
            yield list(batch_np)

    # ------------------------------------------------------------------
    def __len__(self):
        return self.nb

    def set_epoch(self, epoch: int):
        self.epoch = epoch


class InterleavedS1S2BatchSampler:
    """
    每个 batch 按 s1/s2 的实际数据比例自动交替抽样。
    * 一个 epoch 内 100% 用尽 s1 (在 drop_last=False 时)。
    * s2 不足自动过采样。
    * BatchSampler 语义：__iter__ 每次 yield python list[int]。
    * 保证多卡训练时，同一 batch 的数据类型一致，且每个 epoch 各卡处理的数据量一致 (当 drop_last=True)。
    """

    def __init__(
            self,
            dataset,
            batch_size: int = 4,
            # ratio=(7, 1),  # <--- REMOVED: 移除了 ratio 参数
            shuffle: bool = True,
            drop_last: bool = True,
            seed: int = 42,
            interval_range=(150, 200),
    ):
        if not (dist.is_available() and dist.is_initialized()):
            raise RuntimeError("需要先 init_process_group()")

        self.dataset = dataset
        self.batch_size = batch_size
        self.shuffle = shuffle
        self.drop_last = drop_last
        self.seed = seed
        self.intv_rng = interval_range
        self.world = dist.get_world_size()


        s1, s2 = [], []
        for i in range(len(dataset)):
            _, ts = dataset._locate_transition(i)
            (s2 if ts % 200 == 0 else s1).append(i)

        if not s1 or not s2:
            raise ValueError(f"两类样本有一类数量为 0, rank={dist.get_rank()}")

        # 转为 ndarray
        self.s1 = np.asarray(s1, dtype=np.int64)
        self.s2 = np.asarray(s2, dtype=np.int64)

        # 2. 根据 ratio 决定 s2 插入频率 (关键修改)
        # s1_ratio, s2_ratio = ratio # <--- REMOVED

        # <--- MODIFIED: 根据 s1 和 s2 的实际数量比例计算频率
        # 如果 s2 数量为0，前面的 ValueError 会捕获。这里除法是安全的。
        self.s2_freq = max(1, round(len(self.s1) / len(self.s2)))

        # 3. 计算每个 GPU 可迭代的 batch 数 (drop_last 逻辑在这里)
        self.num_batches = len(self.s1) // (batch_size * self.world)
        if not drop_last and len(self.s1) % (batch_size * self.world):
            self.num_batches += 1

        # 4. 需要的 s2 总量，并记下来供 __iter__ 使用
        # 注意：这里的计算现在依赖于自动计算出的 s2_freq
        self.need_s2 = self.num_batches // self.s2_freq + 1
        if len(self.s2) < self.need_s2 * batch_size:
            repeat = math.ceil((self.need_s2 * batch_size) / len(self.s2))
            # 注意：如果 s2 数量极少，repeat 可能会非常大。
            # 下面的写法在 s2 数量小于 batch_size * world_size 时可能依然有问题，需要调整
            # 我们需要保证每个rank上至少有 need_s2 个batch。
            # 因此总共需要 s2 的数量是 need_s2 * batch_size * world
            needed_s2_total_samples = self.need_s2 * batch_size * self.world
            if len(self.s2) < needed_s2_total_samples:
                repeat = math.ceil(needed_s2_total_samples / len(self.s2))
                self.s2 = np.tile(self.s2, repeat)
        self.rank = dist.get_rank()

        # print(f"[Rank {self.rank}] After Broadcast: len(s1) = {len(self.s1)}, len(s2) = {len(self.s2)}")
        # # 计算一个简单的校验和，用于快速判断数组内容是否一致
        # print(f"[Rank {self.rank}] Checksum s1: {np.sum(self.s1)}, Checksum s2: {np.sum(self.s2)}")
        # # 也可以打印前几个元素看看
        # e, ts = dataset._locate_transition(672)
        # print(e, ts)
        # print(f"[Rank {self.rank}] s1[:5]: {self.s1[:min(10, len(self.s1))]}")
        # print(f"[Rank {self.rank}] s2[:5]: {self.s2[:min(10, len(self.s2))]}")

    # __iter__, __len__, set_epoch 方法无需任何修改，保持原样即可
    # ------------------------------------------------------------------
    def __iter__(self):
        epoch = getattr(self, "epoch", 0)
        rng = np.random.default_rng(self.seed + epoch)

        order_s1 = rng.permutation(len(self.s1)) if self.shuffle else np.arange(len(self.s1))
        order_s2 = rng.permutation(len(self.s2)) if self.shuffle else np.arange(len(self.s2))

        rank = dist.get_rank()
        s1_rank = self.s1[order_s1][rank::self.world]
        s2_rank = self.s2[order_s2][rank::self.world]

        ptr1 = ptr2 = 0
        for batch_idx in range(self.num_batches):
            # 🔥 CRITICAL: 确保所有GPU在每一步都使用相同的数据类型
            # Rank 0 决定batch类型，然后广播给所有其他ranks
            if rank == 0:
                is_s2_batch = (batch_idx % self.s2_freq == 1)
                batch_type_tensor = torch.tensor([1 if is_s2_batch else 0], dtype=torch.int32, device='cuda')
            else:
                batch_type_tensor = torch.tensor([0], dtype=torch.int32, device='cuda')
            
            # 广播batch类型决策到所有ranks
            dist.broadcast(batch_type_tensor, src=0)
            is_s2_batch = bool(batch_type_tensor.item())

            if is_s2_batch:
                # S2 batch - 所有GPUs都处理S2数据 (B→C模块)
                if ptr2 + self.batch_size > len(s2_rank):
                    ptr2 = 0
                batch = s2_rank[ptr2: ptr2 + self.batch_size]
                ptr2 += self.batch_size
                
                # 验证采样器分类的正确性
                for idx in batch:
                    _, ts = self.dataset._locate_transition(idx)
                    expected_s2 = (ts % 200 == 0)
                    if not expected_s2:
                        print(f"🚨 SAMPLER ERROR: idx={idx}, ts={ts}, expected S2 but got S1!")
                print(f"SAMPLER {self.rank} batch {batch_idx} / {self.num_batches} {batch} S2_BATCH [SYNC]")

            else:
                # S1 batch - 所有GPUs都处理S1数据 (A→C模块)
                batch = s1_rank[ptr1: ptr1 + self.batch_size]
                ptr1 += self.batch_size
                
                # 验证采样器分类的正确性
                for idx in batch:
                    _, ts = self.dataset._locate_transition(idx)
                    expected_s1 = (ts % 200 != 0)
                    if not expected_s1:
                        print(f"🚨 SAMPLER ERROR: idx={idx}, ts={ts}, expected S1 but got S2!")
                print(f"SAMPLER {self.rank} batch {batch_idx} / {self.num_batches} {batch} S1_BATCH [SYNC]")

            if len(batch) > 0:
                yield batch.tolist()

    # ------------------------------------------------------------------
    def __len__(self):
        return self.num_batches

    def set_epoch(self, epoch: int):
        self.epoch = epoch


import math
import numpy as np
import torch
import torch.distributed as dist
from typing import List, Iterator


class PrefetchSafeInterleavedSampler:
    """
    支持DataLoader预取的安全交替采样器
    通过预计算所有batch类型避免同步问题
    """

    def __init__(
            self,
            dataset,
            batch_size: int = 4,
            shuffle: bool = True,
            drop_last: bool = True,
            seed: int = 42,
    ):
        if not (dist.is_available() and dist.is_initialized()):
            raise RuntimeError("需要先 init_process_group()")

        self.dataset = dataset
        self.batch_size = batch_size
        self.shuffle = shuffle
        self.drop_last = drop_last
        self.seed = seed
        self.world = dist.get_world_size()
        self.rank = dist.get_rank()

        # 分类数据
        s1, s2 = [], []
        for i in range(len(dataset)):
            _, ts = dataset._locate_transition(i)
            (s2 if ts % 200 == 0 else s1).append(i)

        if not s1 or not s2:
            raise ValueError(f"两类样本有一类数量为 0, rank={dist.get_rank()}")

        self.s1 = np.asarray(s1, dtype=np.int64)
        self.s2 = np.asarray(s2, dtype=np.int64)

        # 计算频率和batch数
        self.s2_freq = max(1, round(len(self.s1) / len(self.s2)))
        self.num_batches = len(self.s1) // (batch_size * self.world)
        if not drop_last and len(self.s1) % (batch_size * self.world):
            self.num_batches += 1

        # 🔥 关键：预计算所有batch的类型（全局一致）
        self._precompute_batch_types()

        # 过采样S2数据
        needed_s2_batches = sum(1 for bt in self.batch_types if bt == 'S2')
        needed_s2_samples = needed_s2_batches * batch_size * self.world
        if len(self.s2) < needed_s2_samples:
            repeat = math.ceil(needed_s2_samples / len(self.s2))
            self.s2 = np.tile(self.s2, repeat)

    def _precompute_batch_types(self):
        """预计算所有batch的类型，确保全局一致"""
        self.batch_types = []
        for batch_idx in range(self.num_batches):
            if batch_idx % self.s2_freq == 1:
                self.batch_types.append('S2')
            else:
                self.batch_types.append('S1')

        # 广播batch_types到所有ranks确保一致性
        if self.rank == 0:
            # 将字符串转换为数字进行广播
            batch_type_ints = [1 if bt == 'S2' else 0 for bt in self.batch_types]
            batch_tensor = torch.tensor(batch_type_ints, dtype=torch.int32, device='cuda')
        else:
            batch_tensor = torch.zeros(self.num_batches, dtype=torch.int32, device='cuda')

        dist.broadcast(batch_tensor, src=0)

        # 转换回字符串
        self.batch_types = ['S2' if bt == 1 else 'S1' for bt in batch_tensor.cpu().tolist()]

        print(f"RANK {self.rank}: 预计算 {len(self.batch_types)} 个batch类型: {self.batch_types[:10]}...")

    def __iter__(self) -> Iterator[List[int]]:
        epoch = getattr(self, "epoch", 0)
        
        # 🔥 关键修复：使用全局一致的洗牌，然后直接分配给每个rank对应的batch
        global_rng = np.random.default_rng(self.seed + epoch)
        
        # 生成全局洗牌序列（所有rank一致）
        if self.shuffle:
            order_s1 = global_rng.permutation(len(self.s1))
            order_s2 = global_rng.permutation(len(self.s2))
        else:
            order_s1 = np.arange(len(self.s1))
            order_s2 = np.arange(len(self.s2))

        # 获取洗牌后的全局数据
        s1_global = self.s1[order_s1]
        s2_global = self.s2[order_s2]

        for batch_idx in range(self.num_batches):
            # 使用预计算的batch类型
            batch_type = self.batch_types[batch_idx]
            
            # 计算当前batch在全局数据中的起始位置
            global_batch_start = batch_idx * self.batch_size * self.world
            rank_batch_start = global_batch_start + self.rank * self.batch_size
            rank_batch_end = rank_batch_start + self.batch_size

            if batch_type == 'S2':
                # 从全局S2数据中取当前rank对应的部分
                if rank_batch_end > len(s2_global):
                    # 处理越界：循环使用数据
                    batch_indices = []
                    for i in range(self.batch_size):
                        idx = (rank_batch_start + i) % len(s2_global)
                        batch_indices.append(s2_global[idx])
                    batch = np.array(batch_indices)
                else:
                    batch = s2_global[rank_batch_start:rank_batch_end]
                
                # 验证数据类型正确性
                s2_count = 0
                for idx in batch:
                    _, ts = self.dataset._locate_transition(idx)
                    if ts % 200 == 0:
                        s2_count += 1
                print(f"PREFETCH_SAFE_SAMPLER {self.rank} batch {batch_idx}: {batch} S2_BATCH [PRECOMPUTED] (s2_count={s2_count}/{len(batch)})")
                
            else:  # S1 batch
                # 从全局S1数据中取当前rank对应的部分
                if rank_batch_end > len(s1_global):
                    # 处理越界：截断batch大小或循环使用
                    available = len(s1_global) - rank_batch_start
                    if available <= 0:
                        continue  # 跳过这个batch
                    batch = s1_global[rank_batch_start:rank_batch_start + min(self.batch_size, available)]
                else:
                    batch = s1_global[rank_batch_start:rank_batch_end]
                
                # 验证数据类型正确性
                s1_count = 0
                for idx in batch:
                    _, ts = self.dataset._locate_transition(idx)
                    if ts % 200 != 0:
                        s1_count += 1
                print(f"PREFETCH_SAFE_SAMPLER {self.rank} batch {batch_idx}: {batch} S1_BATCH [PRECOMPUTED] (s1_count={s1_count}/{len(batch)})")

            if len(batch) > 0:
                yield batch.tolist()

    def __len__(self):
        return self.num_batches

    def set_epoch(self, epoch: int):
        self.epoch = epoch