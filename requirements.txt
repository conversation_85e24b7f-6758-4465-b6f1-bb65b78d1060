absl-py==2.1.0
accelerate==1.0.1
aiofiles==23.2.1
aiohappyeyeballs==2.4.0
aiohttp==3.10.5
aiosignal==1.3.1
altair==5.3.0
anyio==4.4.0
appdirs==1.4.4
argcomplete==3.3.0
asciitree==0.3.3
asttokens==2.4.1
async-timeout==4.0.3
attrs==23.2.0
av==12.3.0
backcall==0.2.0
beautifulsoup4==4.12.3
bitsandbytes==0.41.0
cachetools==5.3.3
catkin-pkg==1.0.0
certifi==2024.2.2
charset-normalizer==3.3.2
click==8.1.7
cloudpickle==3.0.0
cmake==3.29.2
colorama==0.3.0
contourpy==1.1.1
cycler==0.12.1
decorator==5.1.1
decord==0.6.0
deepspeed==0.9.5
diffusers==0.11.1
distro==1.9.0
dm-control==1.0.14
dm-env==1.6
dm-tree==0.1.8
docker-pycreds==0.4.0
docutils==0.20.1
egl-probe==1.0.2
einops==0.6.1
einops-exts==0.0.4
evdev==1.7.0
exceptiongroup==1.2.2
executing==2.0.1
fastapi==0.110.2
fasteners==0.19
ffmpy==0.3.2
filelock==3.16.0
fonttools==4.51.0
frozenlist==1.4.1
fsspec==2024.9.0
gdown==5.2.0
gitdb==4.0.11
GitPython==3.1.43
glfw==2.7.0
google-auth==2.29.0
google-auth-oauthlib==1.0.0
gradio==3.35.2
gradio_client==0.2.9
grpcio==1.62.2
gym==0.26.2
gym-notices==0.0.8
h11==0.14.0
h5py==3.11.0
hjson==3.1.0
httpcore==0.17.3
httpx==0.24.0
huggingface-hub==0.25.2
hydra-core==1.2.0
idna==3.7
imageio==2.22.0
imageio-ffmpeg==0.4.9
importlib_resources==6.4.5
ipython==8.12.3
jedi==0.19.1
Jinja2==3.1.4
joblib==1.4.0
jsonschema==4.21.1
jsonschema-specifications==2023.12.1
kiwisolver==1.4.5
labmaze==1.0.6
liger_kernel==0.3.1
linkify-it-py==2.0.3
lit==18.1.3
llvmlite==0.41.1
lxml==5.2.1
Markdown==3.6
markdown-it-py==2.2.0
markdown2==2.4.13
MarkupSafe==2.1.5
matplotlib==3.7.5
matplotlib-inline==0.1.7
mdit-py-plugins==0.3.3
mdurl==0.1.2
mpmath==1.3.0
mujoco==2.3.7
multidict==6.1.0
networkx==3.1
ninja==********
numba==0.58.1
numcodecs==0.12.1
numpy==1.24.4
nvidia-cublas-cu11==**********
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu11==11.7.101
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cuda-nvrtc-cu11==11.7.99
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-runtime-cu11==11.7.99
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cudnn-cu11==********
nvidia-cudnn-cu12==********
nvidia-cufft-cu11==*********
nvidia-cufft-cu12==*********
nvidia-curand-cu11==**********
nvidia-curand-cu12==**********
nvidia-cusolver-cu11==********
nvidia-cusolver-cu12==**********
nvidia-cusparse-cu11==11.7.4.91
nvidia-cusparse-cu12==12.1.0.106
nvidia-nccl-cu11==2.14.3
nvidia-nccl-cu12==2.20.5
nvidia-nvjitlink-cu12==12.6.77
nvidia-nvtx-cu11==11.7.91
nvidia-nvtx-cu12==12.1.105
oauthlib==3.2.2
opencv-python==4.10.0.84
orjson==3.10.1
packaging==24.0
pandas==2.0.3
parso==0.8.4
peft==0.4.0
pexpect==4.9.0
pickleshare==0.7.5
pillow==10.3.0
pkgutil_resolve_name==1.3.10
pluggy==1.5.0
prompt_toolkit==3.0.47
protobuf==3.19.6
psutil==6.0.0
ptyprocess==0.7.0
pure-eval==0.2.2
py-cpuinfo==9.0.0
pyasn1==0.6.0
pyasn1_modules==0.4.0
pydantic==1.10.15
pydub==0.25.1
pygame==2.1.2
Pygments==2.17.2
Pympler==1.1
pymunk==6.2.1
pynput==1.7.6
PyOpenGL==3.1.7
pyparsing==3.1.4
pyquaternion==0.9.9
PySocks==1.7.1
python-dateutil==2.9.0.post0
python-multipart==0.0.9
python-xlib==0.33
pytz==2024.1
PyYAML==6.0.1
qwen-vl-utils==0.0.8
referencing==0.34.0
regex==2024.4.16
requests==2.31.0
requests-oauthlib==2.0.0
# Editable install with no version control (robomimic==0.3.0)
rospkg==1.5.1
rpds-py==0.18.0
rsa==4.9
safetensors==0.4.3
scikit-learn==1.2.2
scipy==1.10.1
semantic-version==2.10.0
sentencepiece==0.1.99
sentry-sdk==1.45.0
setproctitle==1.3.3
Shapely==1.8.4
shortuuid==1.0.13
six==1.16.0
smmap==5.0.1
sniffio==1.3.1
snowballstemmer==2.2.0
soupsieve==2.5
stack-data==0.6.3
starlette==0.37.2
svgwrite==1.4.3
sympy==1.12
tensorboard==2.14.0
tensorboard-data-server==0.7.2
tensorboardX==2.6
termcolor==2.4.0
threadpoolctl==3.4.0
tianshou==0.4.10
timm==0.9.10
tokenizers==0.20.1
toolz==0.12.1
torch==2.4.1
torchvision
tqdm==4.66.5
traitlets==5.14.3
transformers==4.45.2
triton==3.0.0
typing_extensions==4.11.0
tzdata==2024.1
uc-micro-py==1.0.3
urllib3==2.2.3
uvicorn==0.29.0
wandb==0.16.6
wavedrom==2.0.3.post3
wcwidth==0.2.13
websockets==13.0.1
Werkzeug==3.0.2
yarl==1.11.1
zarr==2.16.1
zipp==3.20.1
