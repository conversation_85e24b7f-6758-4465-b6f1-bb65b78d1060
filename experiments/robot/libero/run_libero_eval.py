"""
run_libero_eval.py

Runs a model in a LIBERO simulation environment.

Usage:
    # OpenVLA:
    # IMPORTANT: Set `center_crop=True` if model is fine-tuned with augmentations
    python experiments/robot/libero/run_libero_eval.py \
        --model_family pi0 \
        --pretrained_checkpoint <CHECKPOINT_PATH> \
        --task_suite_name [ libero_spatial | libero_object | libero_goal | libero_10 | libero_90 ] \
        --center_crop [ True | False ] \
        --run_id_note <OPTIONAL TAG TO INSERT INTO RUN ID FOR LOGGING>
"""

import os
import pickle
import sys
from dataclasses import dataclass
from pathlib import Path
from typing import Optional, Union

import draccus
import numpy as np
import torch
import tqdm
from libero.libero import benchmark
from transformers import AutoTokenizer

# Append current directory so that interpreter can find experiments.robot
sys.path.append("../..")
from experiments.robot.libero.libero_utils import (
    get_libero_dummy_action,
    get_libero_env,
    save_rollout_video,
)
from experiments.robot.robot_utils import (
    DATE_TIME,
    get_image_resize_size,
    get_model,
    set_seed_everywhere,
)


@dataclass
class GenerateConfig:
    # fmt: off

    #################################################################################################################
    # Model-specific parameters
    #################################################################################################################
    model_family: str = "pi0"                        # Model family
    pretrained_checkpoint: Union[str, Path] = ""     # Pretrained checkpoint path
    pretrain_vlm_path: Union[str, Path] = ""         # Tokenizer checkpoint path
    center_crop: bool = True                         # Center crop? (if trained w/ random crop image aug)

    #################################################################################################################
    # LIBERO environment-specific parameters
    #################################################################################################################
    task_suite_name: str = "libero_spatial"          # Task suite. Options: libero_spatial, libero_object, libero_goal, libero_10, libero_90
    num_steps_wait: int = 10                         # Number of steps to wait for objects to stabilize in sim
    num_trials_per_task: int = 50                    # Number of rollouts per task

    #################################################################################################################
    # Utils
    #################################################################################################################
    run_id_note: Optional[str] = None                # Extra note to add in run ID for logging
    local_log_dir: str = "./experiments/logs"        # Local directory for eval logs

    seed: int = 7                                    # Random Seed (for reproducibility)


def prepare_inputs(observations, state, task):

    observations = observations / 255.0
    input_item = {}
    img_idx = 0
    for k in ['observation.state', 'observation.images.agentview_rgb', 'observation.images.eye_in_hand_rgb', 'task']:
        if 'images' in k:
            input_item[k] = observations[img_idx].unsqueeze(0).to(dtype=torch.bfloat16, device='cuda')
            img_idx += 1
        elif k == 'task':
            input_item[k] = [task]
        else:
            input_item[k] = torch.from_numpy(state).unsqueeze(0).to(dtype=torch.float32, device='cuda')

    return input_item

@draccus.wrap()
def eval_libero(cfg: GenerateConfig) -> None:
    # Set random seed
    set_seed_everywhere(cfg.seed)

    # Load model
    model = get_model(cfg)
    model.language_tokenizer = AutoTokenizer.from_pretrained(cfg.pretrain_vlm_path)
    p = '/'.join(cfg.pretrained_checkpoint.split('/')[:-1])
    with open(os.path.join(p, "dataset_stats.pkl"), 'rb') as f:
        stats = pickle.load(f)
    model.set_normalize(stats)
    model.to("cuda")

    model.model.state_proj.to(torch.bfloat16)
    model.model.action_time_mlp_in.to(torch.bfloat16)
    model.model.action_time_mlp_out.to(torch.bfloat16)
    model.model.action_out_proj.to(torch.float32)


    # Initialize local logging
    run_id = f"EVAL-{cfg.task_suite_name}-{cfg.model_family}-{DATE_TIME}"
    if cfg.run_id_note is not None:
        run_id += f"--{cfg.run_id_note}"
    os.makedirs(cfg.local_log_dir, exist_ok=True)
    local_log_filepath = os.path.join(cfg.local_log_dir, run_id + ".txt")
    log_file = open(local_log_filepath, "w")
    print(f"Logging to local log file: {local_log_filepath}")

    # Initialize LIBERO task suite
    benchmark_dict = benchmark.get_benchmark_dict()
    task_suite = benchmark_dict[cfg.task_suite_name]()
    num_tasks_in_suite = task_suite.n_tasks
    print(f"Task suite: {cfg.task_suite_name}")
    log_file.write(f"Task suite: {cfg.task_suite_name}\n")

    # Get expected image dimensions
    resize_size = get_image_resize_size(cfg)

    # Start evaluation
    total_episodes, total_successes = 0, 0
    for task_id in tqdm.tqdm(range(num_tasks_in_suite)):
        # Get task
        task = task_suite.get_task(task_id)

        # Get default LIBERO initial states
        initial_states = task_suite.get_task_init_states(task_id)

        # Initialize LIBERO environment and task description
        env, task_description = get_libero_env(task, cfg.model_family, resolution=256)

        # Start episodes
        task_episodes, task_successes = 0, 0
        for episode_idx in tqdm.tqdm(range(cfg.num_trials_per_task)):
            print(f"\nTask: {task_description}")
            log_file.write(f"\nTask: {task_description}\n")

            # Reset environment
            env.reset()

            # Set initial states
            obs = env.set_init_state(initial_states[episode_idx])

            # Setup
            t = 0
            replay_images = []
            if cfg.task_suite_name == "libero_spatial":
                max_steps = 220  # longest training demo has 193 steps
            elif cfg.task_suite_name == "libero_object":
                max_steps = 280  # longest training demo has 254 steps
            elif cfg.task_suite_name == "libero_goal":
                max_steps = 300  # longest training demo has 270 steps
            elif cfg.task_suite_name == "libero_10":
                max_steps = 520  # longest training demo has 505 steps
            elif cfg.task_suite_name == "libero_90":
                max_steps = 400  # longest training demo has 373 steps

            print(f"Starting episode {task_episodes+1}...")
            log_file.write(f"Starting episode {task_episodes+1}...\n")
            while t < max_steps + cfg.num_steps_wait:
                # IMPORTANT: Do nothing for the first few timesteps because the simulator drops objects
                # and we need to wait for them to fall
                if t < cfg.num_steps_wait:
                    obs, reward, done, info = env.step(get_libero_dummy_action(cfg.model_family))
                    t += 1
                    continue

                # Get preprocessed image
                img_agentview_image = obs["agentview_image"]
                replay_images.append(img_agentview_image[::-1,:,:])
                img_agentview_image = np.transpose(img_agentview_image, (2, 0, 1))
                img_eye_in_hand_image = obs["robot0_eye_in_hand_image"]
                img_eye_in_hand_image = np.transpose(img_eye_in_hand_image, (2, 0, 1))
                imgs = torch.stack([torch.from_numpy(img_agentview_image), torch.from_numpy(img_eye_in_hand_image)], dim=0)
                state = np.concatenate([obs["robot0_gripper_qpos"], obs["robot0_eef_pos"], obs["robot0_eef_quat"]])

                # Save preprocessed image for replay video
                # replay_images.append(img_agentview_image)

                # Prepare observations dict
                input_batch = prepare_inputs(imgs, state, task_description)
                input_batch["is_s1"] = True

                # Query model to get action
                if t % 15 == 0:
                    model.reset()
                action, _ = model.select_action(input_batch)

                # Execute action in environment
                obs, reward, done, info = env.step(action.tolist()[0])
                if done:
                    task_successes += 1
                    total_successes += 1

                    break
                t += 1

            task_episodes += 1
            total_episodes += 1
            model.reset()
            # Save a replay video of the episode
            save_rollout_video(
                replay_images, total_episodes, success=done, task_description=task_description, log_file=log_file
            )

            # Log current results
            print(f"Success: {done}")
            print(f"# episodes completed so far: {total_episodes}")
            print(f"# successes: {total_successes} ({total_successes / total_episodes * 100:.1f}%)")
            log_file.write(f"Success: {done}\n")
            log_file.write(f"# episodes completed so far: {total_episodes}\n")
            log_file.write(f"# successes: {total_successes} ({total_successes / total_episodes * 100:.1f}%)\n")
            log_file.flush()

        # Log final results
        print(f"Current task success rate: {float(task_successes) / float(task_episodes)}")
        print(f"Current total success rate: {float(total_successes) / float(total_episodes)}")
        log_file.write(f"Current task success rate: {float(task_successes) / float(task_episodes)}\n")
        log_file.write(f"Current total success rate: {float(total_successes) / float(total_episodes)}\n")
        log_file.flush()

    # Save local log file
    log_file.close()

if __name__ == "__main__":
    eval_libero()
