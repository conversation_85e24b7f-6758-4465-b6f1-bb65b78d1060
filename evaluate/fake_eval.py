import sys
import time
from tqdm import tqdm
sys.path.append('/home/<USER>/tzb/wjj/projects/mirro')
from evaluate.eval_mirro import *
from PIL import Image

def eval_bc_custom(policy, deploy_env, policy_config, raw_lang=None):
    assert raw_lang is not None, "raw lang is None!!!!!!"
    global reasoning
    global check_s2
    global query_frequency
    set_seed(0)
    rand_crop_resize = True

    policy.eval()
    policy = policy.to("cuda")

    max_timesteps = int(1000 * 10)  # may increase for real-world tasks

    print("Start Evaluation")
    for t in tqdm(range(max_timesteps)):

        # image is need only when query is needed
        obs = deploy_env.get_obs(['cam_left_wrist', 'cam_right_wrist', 'cam_top', 'joints'])
        cur_state_np_raw, traj_rgb_np = get_obs(obs, True)

        # todo add resize&crop to wrist camera
        curr_image = torch.from_numpy(traj_rgb_np).float().cuda()
        if rand_crop_resize:
            # print('rand crop resize is used!')
            original_size = curr_image.shape[-2:]
            ratio = 0.95
            curr_image = curr_image[...,
                         int(original_size[0] * (1 - ratio) / 2): int(original_size[0] * (1 + ratio) / 2),
                         int(original_size[1] * (1 - ratio) / 2): int(original_size[1] * (1 + ratio) / 2)]
            curr_image = curr_image.squeeze(0)
            resize_transform = transforms.Resize(original_size, antialias=True)
            curr_image = resize_transform(curr_image)
            traj_rgb_np = curr_image.unsqueeze(0)
            traj_rgb_np = traj_rgb_np.cpu().numpy()

        policy.reset()
        with torch.inference_mode():
            batch = prepare_inputs(traj_rgb_np, cur_state_np_raw, raw_lang, state_repr=policy.config.state_repr)
            batch['is_s1'] = (not getattr(policy.config, "moe", False)) | (t % check_s2 != 0)
            batch['is_s1'] = False
            batch['is_vl_data'] = torch.tensor([False])
            batch['generate_reasoning'] = policy_config['generate_reasoning']

            action, outputs_text = policy.select_action(batch)
        time.sleep(0.5)
        print(batch['task'], outputs_text)

class FakeRobotEnv():
    """Fake robot environment used for testing model evaluation, please replace this to your real environment."""
    def __init__(self, episode_name=None):
        self.real_data = False
        self.time_step = 0
        if episode_name is not None:
            import h5py
            data = h5py.File(episode_name, 'r')
            self.states = np.concatenate((data['state/joint_position/left'][:], data['state/joint_position/right'][:]), axis=1)
            self.images = data['observations']['images']
            self.real_data = True
            pass

    def step(self, action, mode=''):
        print("Execute action successfully!!!")

    def reset(self):
        print("Reset to home position.")

    def get_obs(self, keys):
        if self.real_data:
            obs = {}
            for k,v in self.images.items():
                if 'front' in k:
                    k = k.replace('front', 'bottom')
                if 'high' in k:
                    k = k.replace('high', 'top')
                image = v[self.time_step]
                obs[k] = cv2.imdecode(image, 1)
            states = self.states[self.time_step]
            self.time_step = (self.time_step + 100) % self.states.shape[0]
        else:
            img = cv2.imread('./test.png')
            obs = {k: img for k in keys[:-1]}
            states = np.zeros(14)
        return {
            'images': obs,
            'joints': states,
        }

if __name__ == '__main__':
    policy_config = {

        "model_path": "/home/<USER>/tzb/wjj/train_results/mirro_results/folding_pants_zby_0725_mirro_s1s2_no_fast_from_pi0_1_10_max_token_128/checkpoint-80000",
        "model_path": "/home/<USER>/tzb/wjj/train_results/mirro_results/folding_pants_zby_0725_mirro_s1s2_no_fast_from_pi0_1_10_max_token_128_sg/checkpoint-80000",
        "model_path": "/media/rl/HDD/data/multi_head_train_results/mirro_results/folding_pants_zby_raise_mirro_s1s2_no_fast_from_pi0_1_10_validate/checkpoint-200000",
        "model_path": "/media/rl/HDD/data/multi_head_train_results/mirro_results/folding_pants_zby_raise_mirro_s1s2_co_train_vl_no_fast_mask_generated_tokens_from_pi0_1_10_sg/checkpoint-200000",
        "model_path": "/media/rl/HDD/data/multi_head_train_results/mirro_results/folding_pants_zby_raise_mirro_s2_co_train_vl_no_fast_mask_generated_tokens_from_pi0_correct_with_ac_loss_w10/checkpoint-200000",
        # "model_path": "/home/<USER>/tzb/wjj/train_results/mirro_results/folding_pants_zby_raise_mirro_s2_with_ac_loss_w10_validate_langauge_generation/checkpoint-60000",
        # "model_path": "/home/<USER>/tzb/wjj/train_results/mirro_results/folding_pants_zby_raise_mirro_s2_with_ac_loss_w10_validate_langauge_generation_continuous/checkpoint-60000",
        # "model_path": "/home/<USER>/tzb/wjj/train_results/mirro_results/folding_pants_0703_mirro_s2_with_ac_loss_w1_validate_langauge_generation_continuous_no_action/checkpoint-40000",
        # "model_path": "/home/<USER>/tzb/wjj/train_results/mirro_results/folding_pants_zby_0725_mirro_s1s2_validate_finetune_pi0_weights/checkpoint-150000",
        # "model_path": "/home/<USER>/tzb/wjj/train_results/mirro_results/folding_pants_zby_0725_mirro_s1s2_no_fast_from_pi0_1_10_max_token_128_validate/checkpoint-160000",
        # "model_path": "/home/<USER>/tzb/zhumj/model_param/pi0_lerobot_results/folding_pants_0703_finetune_lang_raw_s1s2_ablate_200000/checkpoint-200000",
        "pretrain_vlm_path": "/media/rl/HDD/data/weights/paligemma_3b/paligemma/pixel_224/paligemma-3b-pt-224-tokenizer",  # for loading tokenizer
        # 'pretrain_vlm_path': "/home/<USER>/tzb/wjj/model_param/PaliGemma/paligemma/pixel_224/vla-paligemma-3b-pt-224/",
        'control_mode': 'absolute',  # absolute
        'generate_reasoning': True,
        'query_frequency': 2,
    }
    global im_size
    im_size = 480  # default 480
    raw_lang = 'Grasp the left hem and left sleeve, then fold them forward.'
    # raw_lang = 'Pull back to flatten the fabric.'
    # raw_lang = 'Grasp the right hem and right sleeve, then fold them backward.'
    # raw_lang = 'Fold to the left.'
    # raw_lang = 'Wrap to the right.'
    # raw_lang = 'Move the folded cloth to right.'
    raw_lang = 'Robot folding light blue pants into compact shape on table.'
    # raw_lang = 'The crumpled shirts are in the basket. Pick it and fold it.'
    raw_lang = 'flatten and orient shirt through continuous spreading, lifting, and repositioning to ensure sleeves and body are clearly visibile in foldable orientation.'
    # raw_lang ='fold pants by manipulating pant legs to bring them together and compact into a near-square shape.'
    raw_lang = "Robotic arms folding dark pants into compact shape."
    # raw_lang = "Robot folding pants into compact shape."
    # raw_lang='Robotic arms flatten, fold, and place a pair of pants on a table.'  # old
    # raw_lang='Robot folding pants into compact shape.'  # new
    # raw_lang='Fold pants.'  # manual

    # >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>policy<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<

    policy = AutoModel.from_pretrained(policy_config['model_path'])
    policy.language_tokenizer = AutoTokenizer.from_pretrained(policy_config['pretrain_vlm_path'])
    p = '/'.join(policy_config['model_path'].split('/')[:-1])
    with open(os.path.join(p, "dataset_stats.pkl"), 'rb') as f:
        stats = pickle.load(f)
    policy.set_normalize(stats)
    if getattr(policy.config, "moe", False):
        policy._init_moe_for_inference()

    # policy.model.state_proj.to(torch.bfloat16)
    policy.model.action_in_proj.to(torch.bfloat16)
    policy.model.action_out_proj.to(torch.bfloat16)
    policy.model.action_time_mlp_in.to(torch.bfloat16)
    policy.model.action_time_mlp_out.to(torch.bfloat16)
    policy.model.action_out_proj.to(torch.float32)

    agilex_bot = FakeRobotEnv("/media/rl/HDD/data/data/mobile_aloha/random_folding_pants_black_zby_20250724_compressed/collection_1753323140.hdf5")
    # agilex_bot = FakeRobotEnv("/home/<USER>/tzb/compressed_data/mobile_aloha_3_wheels/random_folding_pants_blue_zby_20250718_compressed/collection_1752825329.hdf5")
    # agilex_bot = FakeRobotEnv("/home/<USER>/tzb/compressed_data/mobile_aloha_3_wheels/random_folding_pants_brown_wooden_lyp_20250701_compressed/collection_1751360472.hdf5")
    eval_bc_custom(policy, agilex_bot, policy_config, raw_lang=raw_lang)

    print()
    exit()

