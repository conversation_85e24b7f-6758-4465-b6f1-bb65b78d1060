import os
from aloha_scripts.utils import *
from torchvision import transforms
import time
from data_utils.dataset import set_seed
import sys

from models.mirro_s1s2 import *
from eval_deep_dp import get_obs
reasoning = None
check_s2 = 150

def prepare_inputs(observations, state, task):
    observations = observations / 255.0
    input_item = {}
    img_idx = 0
    for k in ['observation.state', 'observation.images.cam_high', 'observation.images.cam_left_wrist', 'observation.images.cam_right_wrist', 'task']:
        if 'images' in k:
            input_item[k] = torch.from_numpy(observations[0][img_idx]).unsqueeze(0).to(dtype=torch.bfloat16, device='cuda')
            img_idx += 1
        elif k == 'task':
            input_item[k] = [task]
        else:
            input_item[k] = torch.from_numpy(state).unsqueeze(0).to(dtype=torch.float32, device='cuda')
    input_item['reasoning'] = None
    return input_item

def eval_bc(policy, deploy_env, policy_config, raw_lang=None):
    assert raw_lang is not None, "raw lang is None!!!!!!"
    global reasoning
    global check_s2
    set_seed(0)

    query_frequency = 25
    rand_crop_resize = False

    policy.eval()
    policy = policy.to("cuda")

    max_timesteps = int(1000 * 10)  # may increase for real-world tasks
    for rollout_id in range(1000):
        rollout_id += 0
        print(f"env has reset!")

        for t in range(max_timesteps):

            if (t + 2) % 200000 == 0:
                a = input(f"q means specify {RED}new Sub Step instructions{RESET}:")
                if a == 'q':
                    lang_in = input("Input new instructions(q and enter mean using default):")
                    if lang_in != 'q' or lang_in != '':
                        raw_lang = lang_in
                        print(raw_lang)

            # image is need only when query is needed
            need_image = (t % query_frequency == 0)
            obs = deploy_env.get_obs(need_image)
            cur_state_np_raw, traj_rgb_np = get_obs(obs, need_image)

            # todo add resize&crop to wrist camera
            if t % query_frequency == 0:
                curr_image = torch.from_numpy(traj_rgb_np).float().cuda()
                if rand_crop_resize:
                    print('rand crop resize is used!')
                    original_size = curr_image.shape[-2:]
                    ratio = 0.95
                    curr_image = curr_image[...,
                                 int(original_size[0] * (1 - ratio) / 2): int(original_size[0] * (1 + ratio) / 2),
                                 int(original_size[1] * (1 - ratio) / 2): int(original_size[1] * (1 + ratio) / 2)]
                    curr_image = curr_image.squeeze(0)
                    resize_transform = transforms.Resize(original_size, antialias=True)
                    curr_image = resize_transform(curr_image)
                    curr_image = curr_image.unsqueeze(0)

            if t % query_frequency == 0:
                policy.reset()

            process_time1 = time.time()
            with torch.inference_mode():
                if t % query_frequency == 0:
                    batch = prepare_inputs(traj_rgb_np, cur_state_np_raw, raw_lang)
                    batch['is_s1'] = (t % check_s2 != 0)
                    if batch['is_s1']:
                        assert reasoning is not None
                        batch['task'] = batch['task'] + reasoning
                else:
                    batch = None
                action, outputs_text = policy.select_action(batch)
                if outputs_text is not None:
                    reasoning = outputs_text

            process_time2 = time.time()

            process_t = process_time2 - process_time1
            # print(f"{RED} Execute >>{query_frequency}<< action costs {time_cur - time_pre - process_t}s. Model forward takes {process_t}s {RESET}")

            print(f'step {t}, pred action: {action}')
            if len(action.shape) == 2:
                action = action[0]
            # action[7:] = 0
            action = action.cpu().numpy()
            action_info = deploy_env.step(action.tolist(), mode=policy_config['control_mode'])

if __name__ == '__main__':
    # >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>hyper parameters<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
    policy_config = {
        "model_path": "/media/eai/Elements/robotics/model_Param/static_aloha_param/pi0_lerobot_results/folding_data_0609_finetune_lang_raw_s1_ablate/checkpoint-50000",
        "pretrain_vlm_path": "/home/<USER>/Documents/wjj/results/lerobot_pi0_results/paligemma-3b-pt-224-tokenizer", # for loading tokenizer
        'control_mode': 'absolute',  # absolute
        "gen_reasoning": False,
        # "stop_gradient": False

    }
    global im_size
    im_size = 480  # default 480
    raw_lang = 'Grasp the left hem and left sleeve, then fold them forward.'
    # raw_lang = 'Pull back to flatten the fabric.'
    # raw_lang = 'Grasp the right hem and right sleeve, then fold them backward.'
    # raw_lang = 'Fold to the left.'
    # raw_lang = 'Wrap to the right.'
    # raw_lang = 'Move the folded cloth to right.'
    raw_lang = 'Robot folding light blue pants into compact shape on table.'
    # raw_lang = 'The crumpled shirts are in the basket. Pick it and fold it.'
    raw_lang='flatten and orient shirt through continuous spreading, lifting, and repositioning to ensure sleeves and body are clearly visibile in foldable orientation.'
    # raw_lang ='fold pants by manipulating pant legs to bring them together and compact into a near-square shape.'
    # raw_lang = "Robot folding brown shorts into compact square."
    raw_lang = "Fold pants"
    raw_lang='Robotic arms flatten, fold, and place a pair of pants on a table.'  # old
    # raw_lang='Robot folding pants into compact shape.'  # new
    # raw_lang='Fold pants.'  # manual


    # >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>policy<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<

    policy = AutoModel.from_pretrained(policy_config['model_path'])
    policy.language_tokenizer = AutoTokenizer.from_pretrained(policy_config['pretrain_vlm_path'])
    p = '/'.join(policy_config['model_path'].split('/')[:-1])
    with open(os.path.join(p, "dataset_stats.pkl"), 'rb') as f:
        stats = pickle.load(f)
    policy.set_normalize(stats)

    policy.model.action_time_mlp_in.to(torch.bfloat16)
    policy.model.action_time_mlp_out.to(torch.bfloat16)
    policy.model.action_out_proj.to(torch.float32)

    sys.path.insert(0, "/home/<USER>/Dev-Code/mirocs")
    from run.agilex_robot_env import AgilexRobot

    agilex_bot = AgilexRobot()

    eval_bc(policy, agilex_bot, policy_config, raw_lang=raw_lang)

    print()
    exit()

