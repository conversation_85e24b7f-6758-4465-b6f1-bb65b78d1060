import os
import pickle
from data_utils.constants import *
import torch
from torchvision import transforms
import cv2
from data_utils.dataset_with_vl import encode_state_to_str
import numpy as np
import time
from transformers import AutoTokenizer, AutoModel
from data_utils.dataset import set_seed
import sys
from models import *
from eval_deep_dp import get_obs
reasoning = None
check_s2 = 1000
query_frequency = 50
def prepare_inputs(observations, state, task, state_repr="continuous"):
    observations = observations / 255.0
    input_item = {}
    img_idx = 0
    for k in ['observation.state', 'observation.images.cam_high', 'observation.images.cam_left_wrist', 'observation.images.cam_right_wrist', 'task']:
        if 'images' in k:
            input_item[k] = torch.from_numpy(observations[0][img_idx]).unsqueeze(0).to(dtype=torch.bfloat16, device='cuda')
            img_idx += 1
        elif k == 'task':
            if state_repr == 'text':
                state_str = encode_state_to_str(torch.from_numpy(state))
                task = f"Task: {task} State: {state_str}."

            input_item[k] = [task]
        else:
            input_item[k] = torch.from_numpy(state).unsqueeze(0).to(dtype=torch.float32, device='cuda')
    input_item['reasoning'] = None
    return input_item

def eval_bc(policy, deploy_env, policy_config, raw_lang=None):
    assert raw_lang is not None, "raw lang is None!!!!!!"
    global reasoning
    global check_s2
    global query_frequency
    set_seed(0)
    rand_crop_resize = True

    policy.eval()
    policy = policy.to("cuda", dtype=torch.bfloat16)

    max_timesteps = int(1000 * 10)  # may increase for real-world tasks
    for rollout_id in range(1000):
        rollout_id += 0
        print(f"env has reset!")

        for t in range(max_timesteps):

            if (t + 2) % 200000 == 0:
                a = input(f"q means specify {RED}new Sub Step instructions{RESET}:")
                if a == 'q':
                    lang_in = input("Input new instructions(q and enter mean using default):")
                    if lang_in != 'q' or lang_in != '':
                        raw_lang = lang_in
                        print(raw_lang)

            # image is need only when query is needed
            if t % query_frequency == 0:
                obs = deploy_env.get_obs(['cam_left_wrist', 'cam_right_wrist', 'cam_top', 'joints'])
            cur_state_np_raw, traj_rgb_np = get_obs(obs, True)

            # todo add resize&crop to wrist camera
            if t % query_frequency == 0:
                curr_image = torch.from_numpy(traj_rgb_np).float().cuda()
                if rand_crop_resize:
                    print('rand crop resize is used!')
                    original_size = curr_image.shape[-2:]
                    ratio = 0.95
                    curr_image = curr_image[...,
                                 int(original_size[0] * (1 - ratio) / 2): int(original_size[0] * (1 + ratio) / 2),
                                 int(original_size[1] * (1 - ratio) / 2): int(original_size[1] * (1 + ratio) / 2)]
                    curr_image = curr_image.squeeze(0)
                    resize_transform = transforms.Resize(original_size, antialias=True)
                    curr_image = resize_transform(curr_image)
                    traj_rgb_np = curr_image.unsqueeze(0)
                    traj_rgb_np = traj_rgb_np.cpu().numpy()

            if t % query_frequency == 0:
                policy.reset()

            process_time1 = time.time()
            with torch.inference_mode():
                if t % query_frequency == 0:
                    batch = prepare_inputs(traj_rgb_np, cur_state_np_raw, raw_lang, state_repr=getattr(policy.config, "state_repr", "continuous"))
                    batch['is_s1'] = (not getattr(policy.config, "moe", False)) | (t % check_s2 != 0)
                    batch['is_vl_data'] = torch.tensor([False])
                    batch['generate_reasoning'] = policy_config['generate_reasoning']
                    if batch['is_s1'] and getattr(policy.config, "moe", False):
                        # assert reasoning is not None
                        # batch['task'][0] = batch['task'][0] + reasoning
                        policy.reset()
                    else:
                        print(t)
                else:
                    batch = None
                action, outputs_text = policy.select_action(batch)
                # print(f"Current Reasoning: {reasoning}")
                if outputs_text is not None and getattr(policy.config, "moe", False):
                    reasoning = outputs_text
                    # input("input any key to continue (check to s2)")

            process_time2 = time.time()

            process_t = process_time2 - process_time1
            # print(f"{RED} Execute >>{query_frequency}<< action costs {time_cur - time_pre - process_t}s. Model forward takes {process_t}s {RESET}")

            # print(f'step {t}, pred action: {action}')
            if len(action.shape) == 2:
                action = action[0]
            # action[7:] = 0
            action = action.cpu().numpy()
            action_info = deploy_env.step(action.tolist())
            time.sleep(0.006)


if __name__ == '__main__':
    # >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>hyper parameters<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
    policy_config = {
        # "model_path": "/media/midea/MAD-1/wjj/mirro_results/folding_pants_0703_mirro_validate_finetune_pi0_weights/checkpoint-60000",
        # "model_path": "/media/midea/MAD-1/wjj/mirro_results/folding_pants_0703_mirro_s1s2_validate_finetune_pi0_weights/checkpoint-60000",
        # "model_path": "/media/midea/MAD-1/wjj/mirro_results/folding_pants_zby_raise_mirro_xdof_finetune/checkpoint-60000",
        "model_path": "/media/midea/MAD-1/wjj/mirro_results/folding_pants_zby_raise_mirro_xdof_finetune_mirro_weights_joy/checkpoint-60000", # most smooth
        # "model_path": "/media/midea/MAD-1/wjj/mirro_results/folding_pants_zby_raise_mirro_s2_co_train_vl_with_fast_mask_generated_tokens_sg/checkpoint-80000",
        # "model_path": "/media/midea/MAD-1/wjj/mirro_results/folding_pants_zby_raise_mirro_s2_co_train_vl_no_fast_mask_generated_tokens/checkpoint-100000",
        # "model_path": "/media/midea/MAD-1/wjj/mirro_results/folding_pants_zby_raise_mirro_s2_co_train_vl_no_fast_mask_generated_tokens_from_pi0/checkpoint-100000",
        # "model_path": "/media/midea/MAD-1/wjj/mirro_results/folding_pants_zby_raise_mirro_s2_co_train_vl_no_fast_mask_generated_tokens_sg2/checkpoint-100000",
        "model_path": "/media/midea/MAD-1/wjj/mirro_results/folding_pants_zby_raise_mirro_s2_co_train_vl_no_fast_mask_generated_tokens_from_pi0_correct_with_ac_loss_w10/checkpoint-200000", # best for co train
        # "model_path": "/media/midea/MAD-1/wjj/mirro_results/folding_pants_zby_raise_mirro_s1s2_co_train_vl_no_fast_mask_generated_tokens_from_pi0_1_10/checkpoint-200000",
        "model_path": "/media/midea/MAD-1/wjj/mirro_results/folding_pants_zby_raise_mirro_s2_co_train_vl_no_fast_mask_generated_tokens_from_pi0_correct_with_ac_loss_w10_sg/checkpoint-200000", # best for co train
        # "model_path": "/media/midea/MAD-1/wjj/mirro_results/folding_pants_zby_raise_mirro_s1s2_co_train_vl_no_fast_mask_generated_tokens_from_pi0_1_10_sg/checkpoint-200000",
        # "model_path": "/media/midea/MAD-1/wjj/mirro_results/folding_pants_zby_raise_mirro_s1s2_no_fast_from_pi0_1_10_validate/checkpoint-200000",
        "model_path": "/media/midea/MAD-1/wjj/mirro_results/folding_pants_zby_0725_mirro_s2_vl_with_ac_loss_w10_quantile_ada_rms_norm/checkpoint-90000",
        "model_path": "/media/midea/MAD-1/wjj/mirro_results/folding_pants_zby_0725_mirro_s2_vl_with_ac_loss_w10_validate/checkpoint-90000",
        "pretrain_vlm_path": "/home/<USER>/wjj/paligemma-3b-pt-224-tokenizer", # for loading tokenizer
        'control_mode': 'absolute',  # absolute
        'generate_reasoning': False,
    }
    global im_size
    im_size = 480  # default 480
    raw_lang = 'Grasp the left hem and left sleeve, then fold them forward.'
    # raw_lang = 'Pull back to flatten the fabric.'
    # raw_lang = 'Grasp the right hem and right sleeve, then fold them backward.'
    # raw_lang = 'Fold to the left.'
    # raw_lang = 'Wrap to the right.'
    # raw_lang = 'Move the folded cloth to right.'
    raw_lang = 'Robot folding light blue pants into compact shape on table.'
    # raw_lang = 'The crumpled shirts are in the basket. Pick it and fold it.'
    raw_lang='flatten and orient shirt through continuous spreading, lifting, and repositioning to ensure sleeves and body are clearly visibile in foldable orientation.'
    # raw_lang ='fold pants by manipulating pant legs to bring them together and compact into a near-square shape.'
    # raw_lang = "Robot folding brown shorts into compact square."
    raw_lang = "Robot folding gray pants into compact shape."
    # raw_lang='Robotic arms flatten, fold, and place a pair of pants on a table.'  # old
    # raw_lang='Robot folding pants into compact shape.'  # new
    # raw_lang='Fold pants.'  # manual

    # >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>policy<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<

    policy = AutoModel.from_pretrained(policy_config['model_path'])
    policy.language_tokenizer = AutoTokenizer.from_pretrained(policy_config['pretrain_vlm_path'])
    p = '/'.join(policy_config['model_path'].split('/')[:-1])
    with open(os.path.join(p, "dataset_stats.pkl"), 'rb') as f:
        stats = pickle.load(f)
    policy.set_normalize(stats)
    if getattr(policy.config, "moe", False):
        policy._init_moe_for_inference()
    policy.model.action_out_proj.to(torch.float32)

    from arx_bot.arx_bot_env import ArxRobot

    agilex_bot = ArxRobot()
    agilex_bot.reset()
    eval_bc(policy, agilex_bot, policy_config, raw_lang=raw_lang)

    print()
    exit()

