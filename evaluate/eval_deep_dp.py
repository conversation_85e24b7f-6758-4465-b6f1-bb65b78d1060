import os
import pickle
import sys
import cv2
import time
import torch

import numpy as np
from data_utils.dataset import set_seed
from torchvision import transforms
from transformers import AutoTokenizer, AutoModel


def get_obs(deplot_env_obs, need_image=True):
    if need_image:
        cur_top_rgb = deplot_env_obs['images']['cam_top']  # camera_extrinsics image
        cur_left_rgb = deplot_env_obs['images']['cam_left_wrist']  # camera_extrinsics image
        cur_right_rgb = deplot_env_obs['images']['cam_right_wrist']  # camera_extrinsics image

        cur_top_rgb = cv2.resize(cv2.cvtColor(cur_top_rgb, cv2.COLOR_BGRA2BGR), (320, 240))
        cur_left_rgb = cv2.resize(cv2.cvtColor(cur_left_rgb, cv2.COLOR_BGRA2BGR), (320, 240))
        cur_right_rgb = cv2.resize(cv2.cvtColor(cur_right_rgb, cv2.COLOR_BGRA2BGR), (320, 240))
        # [128, 128, 3] np array
        right_rgb_img = cur_right_rgb
        left_rgb_img = cur_left_rgb

        traj_rgb_np = np.array([cur_top_rgb, left_rgb_img, right_rgb_img])
        traj_rgb_np = np.expand_dims(traj_rgb_np, axis=1)
        traj_rgb_np = np.transpose(traj_rgb_np, (1, 0, 4, 2, 3))

        print("#" * 50)
        print(traj_rgb_np.shape)
    else:
        traj_rgb_np = None

    cur_joint_positions = deplot_env_obs['joints']
    return cur_joint_positions, traj_rgb_np

def prepare_inputs(observations, state, task):
    observations = observations / 255.0
    input_item = {}
    img_idx = 0
    for k in ['observation.state', 'observation.images.cam_high', 'observation.images.cam_left_wrist', 'observation.images.cam_right_wrist', 'task']:
        if 'images' in k:
            input_item[k] = observations[0][img_idx].unsqueeze(0).to(dtype=torch.bfloat16, device='cuda')
            img_idx += 1
        elif k == 'task':
            input_item[k] = [task]
        else:
            input_item[k] = torch.from_numpy(state).unsqueeze(0).to(dtype=torch.float32, device='cuda')

    return input_item

def eval_bc(policy, deploy_env, policy_config, raw_lang=None, tokenizer=None, lang_model=None):
    assert raw_lang is not None, "raw lang is None!!!!!!"
    set_seed(0)

    query_frequency = 25
    rand_crop_resize = False

    policy.eval()
    policy = policy.to("cuda")

    max_timesteps = int(1000 * 10)  # may increase for real-world tasks
    for rollout_id in range(1000):
        rollout_id += 0
        print(f"env has reset!")

        for t in range(max_timesteps):

            if (t + 2) % 200000 == 0:
                a = input(f"q means specify {RED}new Sub Step instructions{RESET}:")
                if a == 'q':
                    lang_in = input("Input new instructions(q and enter mean using default):")
                    if lang_in != 'q' or lang_in != '':
                        raw_lang = lang_in
                        print(raw_lang)

            # image is need only when query is needed
            need_image = (t % query_frequency == 0)
            obs = deploy_env.get_obs(need_image)
            cur_state_np_raw, traj_rgb_np = get_obs(obs, need_image)

            # todo add resize&crop to wrist camera
            if t % query_frequency == 0:
                curr_image = torch.from_numpy(traj_rgb_np).float().cuda()
                if rand_crop_resize:
                    print('rand crop resize is used!')
                    original_size = curr_image.shape[-2:]
                    ratio = 0.95
                    curr_image = curr_image[...,
                                 int(original_size[0] * (1 - ratio) / 2): int(original_size[0] * (1 + ratio) / 2),
                                 int(original_size[1] * (1 - ratio) / 2): int(original_size[1] * (1 + ratio) / 2)]
                    curr_image = curr_image.squeeze(0)
                    resize_transform = transforms.Resize(original_size, antialias=True)
                    curr_image = resize_transform(curr_image)
                    curr_image = curr_image.unsqueeze(0)

            if t % query_frequency == 0:
                policy.reset()

            with torch.inference_mode():
                batch = prepare_inputs(curr_image, cur_state_np_raw, raw_lang)
                if policy.config.oft:
                    enc_lang = encode_lang(raw_lang, tokenizer, lang_model).to("cuda").unsqueeze(0)
                else:
                    enc_lang = None
                batch["lang_embeddings"] = enc_lang
                action = policy.select_action(batch)

            print(f'step {t}, pred action: {action}')
            if len(action.shape) == 2:
                action = action[0]
            # action[7:] = 0
            action_info = deploy_env.step(action.tolist(), mode=policy_config['control_mode'])

if __name__ == '__main__':
    # >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>hyper parameters<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
    policy_config = {
        # "model_path": "/media/eai/Elements/robotics/model_Param/static_aloha_param/pi0_lerobot_results/folding_data_0609_no_lang/checkpoint-40000",
        "model_path": "/media/eai/Elements/robotics/model_Param/static_aloha_param/pi0_lerobot_results/folding_data_0609_film_sub_reason/checkpoint-50000",
        "model_path": "/media/eai/Elements/robotics/model_Param/static_aloha_param/pi0_lerobot_results/folding_data_0609_paligemma_s1_no_lang/checkpoint-90000",
        "camera_views": 3,
        'control_mode': 'absolute',  # absolute

    }
    raw_lang = 'Grasp the left hem and left sleeve, then fold them forward.'
    raw_lang = 'fold pants by manipulating pant legs, bringing them together, and forming compact near - square shape.'
    # raw_lang = "place folded brown pants at final position and withdraw robotic arms."

    distillbert_dir = "/home/<USER>/Documents/zhumj/model_param/mllm/distilbert-base-uncased"

    # >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> policy <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
    policy = AutoModel.from_pretrained(policy_config['model_path'])
    p = '/'.join(policy_config['model_path'].split('/')[:-1])
    with open(os.path.join(p, "dataset_stats.pkl"), 'rb') as f:
        stats = pickle.load(f)
    policy.set_normalize(stats)

    tokenizer = None
    lang_model = None
    if policy.config.oft:
        tokenizer = AutoTokenizer.from_pretrained(distillbert_dir)
        lang_model = AutoModel.from_pretrained(distillbert_dir, torch_dtype=torch.bfloat16).to("cuda")

    # >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> robot env <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
    sys.path.insert(0, "/home/<USER>/Dev-Code/mirocs")
    from run.agilex_robot_env import AgilexRobot
    agilex_bot = AgilexRobot()

    # >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> eval bc <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
    eval_bc(policy, agilex_bot, policy_config, raw_lang=raw_lang, tokenizer=tokenizer, lang_model=lang_model)